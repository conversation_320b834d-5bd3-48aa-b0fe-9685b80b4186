const {
  GraphQLNonNull,
  GraphQLString,
  GraphQLInt,
  GraphQLFloat
} = require('graphql')
const type = require('./type')
const pobqrActivationController = require('../../controller/pobqrActivation/pobqrActivationController')

// Defines the queries
module.exports = {
  fetchPOBFormData: {
    type,
    args: {
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
    },
    resolve: pobqrActivationController.fetchPOBFormData.bind(pobqrActivationController)
  },
  checkQRStatus: {
    type,
    args: {
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
    },
    resolve: pobqrActivationController.checkQRStatus.bind(pobqrActivationController)
  }
}
