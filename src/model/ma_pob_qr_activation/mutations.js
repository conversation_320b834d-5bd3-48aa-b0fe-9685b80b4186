const {
  GraphQLNonNull,
  GraphQLString,
  GraphQLInt
} = require('graphql')
const type = require('./type')
const pobqrActivationController = require('../../controller/pobqrActivation/pobqrActivationController')
 
module.exports = {
  pobQrUpiFormData: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
 
      shop_name: {
        type: new GraphQLNonNull(GraphQLString)
      },
      company_type: {
        type: new GraphQLNonNull(GraphQLString)
      },
      type_of_establishment: {
        type: new GraphQLNonNull(GraphQLString)
      },
      shop_address: {
        type: new GraphQLNonNull(GraphQLString)
      },
      shop_city: {
        type: new GraphQLNonNull(GraphQLString)
      },
      shop_district: {
        type: new GraphQLNonNull(GraphQLString)
      },
      shop_state: {
        type: new GraphQLNonNull(GraphQLString)
      },
      shop_pincode: {
        type: new GraphQLNonNull(GraphQLString)
      },
      annual_transaction_volume: {
        type: new GraphQLNonNull(GraphQLString)
      }
 
    },
    resolve: pobqrActivationController.pobQrUpiFormData.bind(pobqrActivationController)
  },
  updloadDocument: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
 
      document: {
        type: new GraphQLNonNull(GraphQLString)
      },
      path: {
        type: new GraphQLNonNull(GraphQLString)
      }
    },
    resolve: pobqrActivationController.updloadDocument.bind(pobqrActivationController)
  }
}
 
 