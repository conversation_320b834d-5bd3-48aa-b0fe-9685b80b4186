const {
  GraphQLNonNull,
  GraphQLInt,
  GraphQLString
} = require('graphql')
const type = require('./type')
const commissionReportController = require('../../controller/commissionReport/commissionReportController')
const { default: GraphQLJSON } = require('graphql-type-json')

// Defines the queries
module.exports = {
  getCommissionReport: {
    type,
    args: {
      ma_user_id: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      userid: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      start_date: {
        type: GraphQLString
      },
      end_date: {
        type: GraphQLString
      },
      parent_id: {
        type: GraphQLString
      },
      transaction_type: {
        type: GraphQLString
      },
      limit: {
        type: new GraphQLNonNull(GraphQLInt)
      },
      offset: {
        type: new GraphQLNonNull(GraphQLInt)
      }
    },
    resolve: commissionReportController.getCommissionReport.bind(commissionReportController)
  }

}
