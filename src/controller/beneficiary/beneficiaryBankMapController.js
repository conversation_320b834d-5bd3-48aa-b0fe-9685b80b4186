const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const dmtSession = require('../session/dmt_session')
const validator = require('../../util/validator')
const Routing = require('../bankRouting/routing')
const bankOnBoardDetails = require('../bankHandler/bankOnBoardingDetails')
const util = require('../../util/util')
const sms = require('../../util/sms')
const { getTryBankArray } = require('../../util/common_fns')
const otp = require('../otp/otpController')
const bankBranch = require('../bankDetails/bankBranchController')
// const { lokiLogger } = require('../../util/lokiLogger')

class BeneficiaryBankMap extends DAO {
  /**
     * Overrides TABLE_NAME with this class' backing table at MySQL
     */
  get TABLE_NAME () {
    return 'ma_beneficiary_bank_mapping'
  }

  static get PRIMARY_KEY () {
    return 'ma_beneficiary_bank_mapping_id'
  }

  static async findMatching (_, fields) {
    this.TABLE_NAME = 'ma_beneficiary_bank_mapping'
    log.logger({ pagename: require('path').basename(__filename), action: 'findMatching', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'findMatching', type: 'request', fields: fields })
    // Returns early with all transactions if no criteria was passed
    if (Object.keys(fields).length === 0) return this.findAll()

    // Find matching transactions
    const res = await this.findByFields({
      fields
    })
    log.logger({ pagename: require('path').basename(__filename), action: 'findMatching', type: 'response', fields: res })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'findMatching', type: 'response', fields: res })
    return res
  }

  static async createEntry (_, fields, connection = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'request', fields: fields })
    var isSet = false
    try {
      if (connection === null || connection === undefined) {
        connection = await mySQLWrapper.getConnectionFromPool()
        isSet = true
      }

      const _result = await this.insert(connection, {
        data: fields
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'response', fields: _result })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'response', fields: _result })
      if (_result.affectedRows > 0) {
        return { status: 200, message: 'success', insertId: _result.insertId }
      } else {
        return { status: 400, respcode: 1022, message: errorMsg.responseCode[1022] }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'createEntry', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) connection.release()
    }
  }

  static async updateWhereData (connection, { data, id, where }) {
    this.TABLE_NAME = 'ma_beneficiary_bank_mapping'
    const updateQueue = await this.updateWhere(connection, { data, id: id, where: where })
    return updateQueue
  }

  static async isBeneRegister (uic, ma_beneficiaries_id, ma_bank_on_boarding_id, conn) {
    log.logger({ pagename: require('path').basename(__filename), action: 'isBeneRegister', type: 'request', fields: { uic, ma_bank_on_boarding_id } })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'isBeneRegister', type: 'request', fields: { uic, ma_bank_on_boarding_id } })
    const tmpConn = !conn
    const connection = conn || await mySQLWrapper.getConnectionFromPool()
    try {
      const beneNActiveExist = `SELECT 
      b.account_number
      FROM ma_beneficiaries as b 
      WHERE b.uic = '${uic}'
      AND b.ma_beneficiaries_id = ${ma_beneficiaries_id}
      AND b.beneficiary_status = 'Y'
      limit 1
      `

      log.logger({ pagename: require('path').basename(__filename), action: 'isRegisterBankListSQL', type: 'beneNActiveExistSQL', fields: beneNActiveExist })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'isRegisterBankListSQL', type: 'beneNActiveExistSQL', fields: beneNActiveExist })
      const isRegisterBeneList = await this.rawQuery(beneNActiveExist, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'isBeneRegister', type: 'isRegisterBeneList', fields: isRegisterBeneList })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'isBeneRegister', type: 'isRegisterBeneList', fields: isRegisterBeneList })

      let respocodeRegister = {}

      if (isRegisterBeneList.length > 0) {
        const sql = `SELECT 
      map.*,
      b.account_number,
      b.ifsc_code,
      b.mobile_number,
      b.ben_mobile_number,
      mbm.bank_name as bene_bank_name
      FROM ma_beneficiary_bank_mapping as map
      JOIN ma_beneficiaries as b ON
      b.ma_beneficiaries_id = map.ma_beneficiaries_id       
      JOIN ma_bank_master as mbm ON
      b.ma_bank_master_id = mbm.ma_bank_master_id
      WHERE b.uic = '${uic}' AND map.ma_bank_on_boarding_id = ${ma_bank_on_boarding_id}
      AND b.ma_beneficiaries_id = ${ma_beneficiaries_id}
      AND b.beneficiary_status = 'Y'
      AND map.bank_status IN ('S','P') limit 1
      `

        log.logger({ pagename: require('path').basename(__filename), action: 'isRegisterBankListSQL', type: 'isRegisterBankList', fields: sql })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'isRegisterBankListSQL', type: 'isRegisterBankList', fields: sql })
        const isRegisterBankList = await this.rawQuery(sql, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'isBeneRegister', type: 'isRegisterBankList', fields: isRegisterBankList })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'isBeneRegister', type: 'isRegisterBankList', fields: isRegisterBankList })

        if (isRegisterBankList.length > 0) {
          respocodeRegister = { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, data: isRegisterBankList[0] }
        } else {
          respocodeRegister = { status: 200, message: errorMsg.responseCode[1002], respcode: 1002, data: [] }
        }
      } else {
        respocodeRegister = { status: 400, message: errorMsg.responseCode[1002] + '~[BENE_EXIST]', respcode: 1002, data: [] }
      }

      return respocodeRegister
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'isBeneRegister', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'isBeneRegister', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001] + ' [IS_REGISTER]', respcode: 1001 }
    } finally {
      if (tmpConn) connection.release()
    }
  }

  static async getTransferBankList (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getTransferBankList', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getTransferBankList', type: 'request', fields: fields })

    if (!validator.definedVal(fields.countrycode)) {
      fields.countrycode = 'IN'
    }

    const validate = validator.validateMobile(fields.mobile_number, fields.countrycode)
    if (validate.status === false) {
      return { status: 400, respcode: 1033, message: errorMsg.responseCode[1033] }
    }

    if (!validator.definedVal(fields.uic)) {
      return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051] }
    }

    /* DMT SESSION DATA */

    if (!validator.definedVal(fields.sessionRQ)) {
      return { status: 400, respcode: 1161, message: errorMsg.responseCode[1161] }
    }

    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sql = `SELECT remitter_id, mobile_number FROM ma_customer_details WHERE uic = '${fields.uic}' AND customer_status = 'Y'`

      const remitterResp = await this.rawQuery(sql, connection)
      if (remitterResp.length <= 0) {
        // return error - customer not found
        return {
          status: 400,
          respcode: 1115,
          message: errorMsg.responseCode[1115] + ' [UIC~' + fields.uic + ']'
        }
      }

      const sql2 = `SELECT ma_beneficiaries_id, uic, account_number, ifsc_code ,ma_bank_master_id FROM ma_beneficiaries WHERE uic = '${fields.uic}' AND ma_beneficiaries_id = ${fields.ma_beneficiaries_id}`
      console.log('sql2', sql2)
      const beneRegisterResp = await this.rawQuery(sql2, connection)
      if (beneRegisterResp.length <= 0) {
        // return error - customer not found
        return {
          status: 400,
          respcode: 1168,
          message: errorMsg.responseCode[1168] + ' [UIC~' + fields.uic + ']' + ' [BENE~' + fields.ma_beneficiaries_id + ']'
        }
      }

      const sessionData = await dmtSession.getDMTSessionData(connection, fields.sessionRQ, {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid,
        uic: fields.uic
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'getTransferBankList', type: 'sessionData', fields: sessionData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getTransferBankList', type: 'sessionData', fields: sessionData })

      if (sessionData.status != 200) {
        return sessionData
      }

      const { bank_name, ma_bank_on_boarding_id, uicSession, bank_priority_json } = sessionData.data
      const handler = sessionData.handler
      // check given customer register to bank side or not
      const customBankMap = require('../customer/customerBankMapController')
      const isCustomerRegister = await customBankMap.isCustomerRegister(fields.uic, ma_bank_on_boarding_id, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'getTransferBankList', type: 'isCustomerRegister', fields: isCustomerRegister })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getTransferBankList', type: 'isCustomerRegister', fields: isCustomerRegister })

      if (isCustomerRegister.status != 200) {
        return isCustomerRegister
      }

      if (isCustomerRegister.status == 200 && isCustomerRegister.data.length == 0) {
        let message = errorMsg.responseCode[1165]
        message = message.replace('#mobile', fields.mobile_number)
        message = message.replace('#any', bank_name)

        return { status: 400, message: message + ' [IS_REGISTER]', respcode: 1165 }
      }

      // uic, ma_beneficiaries_id, ma_bank_on_boarding_id, conn
      const isBeneRegister = await this.isBeneRegister(fields.uic, fields.ma_beneficiaries_id, ma_bank_on_boarding_id, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'getTransferBankList', type: 'isBeneRegister', fields: isBeneRegister })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getTransferBankList', type: 'isBeneRegister', fields: isBeneRegister })

      // check given bene register to bank side or not
      if (isBeneRegister.status != 200) {
        return isBeneRegister
      }

      const addAutoBene = await bankOnBoardDetails.getBanksDetails(null, { ma_bank_on_boarding_id: ma_bank_on_boarding_id, entity_type: 'AUTO_ADD_BENE' }, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'checkTransferRegister', type: 'addAutoBene', fields: addAutoBene })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'checkTransferRegister', type: 'addAutoBene', fields: addAutoBene })

      if (
        addAutoBene.status !== 200 ||
        addAutoBene.data.length == 0
      ) {
        return addAutoBene
      }
      const addAutoBeneData = addAutoBene.data[0]

      const addAsPending = addAutoBeneData.otp_required === 'YES'

      // Bene is not register to given on board bank
      if (isBeneRegister.status == 200 && isBeneRegister.data.length == 0) {
        // Check Bene check required or not, if already Register with bank of with different ifsc code the delete
        // bene and add with new ifsc code

        const checkandDeleteBene = await this.checkBeneExistDeleteExist('', {
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          mobile_number: fields.mobile_number,
          uic: fields.uic,
          sessionRQ: fields.sessionRQ,
          account_number: beneRegisterResp[0].account_number,
          ifsc_code: beneRegisterResp[0].ifsc_code
        })

        log.logger({ pagename: require('path').basename(__filename), action: 'getTransferBankList', type: 'checkandDeleteBene', fields: checkandDeleteBene })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getTransferBankList', type: 'checkandDeleteBene', fields: checkandDeleteBene })

        const addBeneficiaryBankRes = await this.addBeneficiaryBank(handler, { ma_beneficiaries_id: fields.ma_beneficiaries_id, ma_user_id: fields.ma_user_id, user_id: fields.userid, sessionRQ: fields.sessionRQ, addAsPending }, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'getTransferBankList', type: 'addBeneficiaryBankRes', fields: addBeneficiaryBankRes })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getTransferBankList', type: 'addBeneficiaryBankRes', fields: addBeneficiaryBankRes })
        if (addBeneficiaryBankRes.status == 400) {
          return addBeneficiaryBankRes
        }
      }

      // Bene is is register to given on board bank let get avaiable transfer limit as per
      // priorites
      // get bank side limit wherever remitter is register
      const customerLogin = require('../customer/customerController')
      console.time('TIMER_DMT_LIMIT')
      const bankLimitResponse = await customerLogin.getRemitterLimit('_', { uic: uicSession, ma_user_id: fields.ma_user_id, userid: fields.userid, mobile_number: validate.number, sessionRQ: 'internal', sessionData: sessionData }, connection)
      console.timeEnd('TIMER_DMT_LIMIT')
      console.log('limitResponse', bankLimitResponse)
      log.logger({ pagename: require('path').basename(__filename), action: 'getTransferBankList', type: 'bankLimitResponse', fields: bankLimitResponse })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getTransferBankList', type: 'bankLimitResponse', fields: bankLimitResponse })

      const bankDownQuery = `SELECT ratio, failure_count, threshold FROM ma_dmt_bank_down_logs WHERE ma_bank_master_id = ${beneRegisterResp[0].ma_bank_master_id} AND addedon BETWEEN DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 15 MINUTE) AND CURRENT_TIMESTAMP LIMIT 1;`
      const bankDownResult = await this.rawQuery(bankDownQuery, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getTransferBankList', type: 'response-query', fields: { response: bankDownResult, query: bankDownQuery } })
      let bank_down_notification

      if (bankDownResult.length > 0 && bankDownResult[0].failure_count > bankDownResult[0].threshold) {
        bank_down_notification = 'Beneficiary Bank is currently unavailable, you may try using different bank account or please try again after sometime.'
      }

      if (bankLimitResponse.status === 200 && bankLimitResponse.bankListLimits.length > 0) {
        const bankListLimits = bankLimitResponse.bankListLimits
        const bankPriorityListArr = JSON.parse(bank_priority_json)
        bankListLimits.forEach(bankItem => {
          for (let index = 0; index < bankPriorityListArr.length; index++) {
            const priortyItem = bankPriorityListArr[index]
            if (bankItem.bank_name == priortyItem.bank_name) {
              if (!validator.definedVal(bankItem.transfer_mode)) {
                bankItem.transfer_mode = []
              }
              bankItem.transfer_mode.push(priortyItem)
            }
          }
        })

        return {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          bank_down_notification,
          transferBankList: bankListLimits || []
        }
      } else {
        return { ...bankLimitResponse, bank_down_notification, transferBankList: [] }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getTransferBankList', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getTransferBankList', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001] + ' [IS_REGISTER]', respcode: 1001 }
    } finally {
      connection.release()
    }
  }

  static async addBeneficiaryBank (handler, { ma_beneficiaries_id, ma_user_id, user_id, sessionRQ, isOtpRequired, orderid, otp, addAsPending = false }, conn = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryBank', type: 'request', fields: { handler, ma_beneficiaries_id, ma_user_id, user_id } })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiaryBank', type: 'request', fields: { handler, ma_beneficiaries_id, ma_user_id, user_id } })
    this.TABLE_NAME = 'ma_beneficiaries'
    const tmpConn = !conn
    const connection = conn || await mySQLWrapper.getConnectionFromPool()
    try {
      if (handler) {
        // call bank api integration
        // BANK API CODE STARTS
        const beneficiaryDetailData = await bankOnBoardDetails.getBeneficiaryDetails(null, { beneficiaryId: ma_beneficiaries_id, ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID })
        if (beneficiaryDetailData.status === 200) {
          const tmpbenData = beneficiaryDetailData.data[0]

          const sql = `SELECT 
                        map.*
                        FROM ma_beneficiary_bank_mapping as map
                        JOIN ma_beneficiaries as b on b.ma_beneficiaries_id = map.ma_beneficiaries_id
                        WHERE b.uic = '${tmpbenData.uic}' AND map.ma_bank_on_boarding_id = ${handler.BANK_ON_BOARDING_ID}
                        AND b.ifsc_code = '${tmpbenData.ifscode}'
                        AND b.account_number = '${tmpbenData.accountnumber}'
                        AND b.ma_beneficiaries_id = ${ma_beneficiaries_id}
                        limit 1
      `

          log.logger({ pagename: require('path').basename(__filename), action: 'isRegisterBankListSQL', type: 'isRegisterBankList', fields: sql })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'isRegisterBankListSQL', type: 'isRegisterBankList', fields: sql })
          const isRegisterBankList = await this.rawQuery(sql, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'isBeneRegister', type: 'isRegisterBankList', fields: isRegisterBankList })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'isBeneRegister', type: 'isRegisterBankList', fields: isRegisterBankList })

          const beneBankReferData = {}
          let bankCallRequired = true
          if (isRegisterBankList.length > 0) {
            if (ma_beneficiaries_id == isRegisterBankList[0].ma_beneficiaries_id && isRegisterBankList[0].bank_status == 'S') {
              return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, bankName: handler.BANK_NAME }
            }

            if (ma_beneficiaries_id != isRegisterBankList[0].ma_beneficiaries_id &&
              isRegisterBankList[0].bank_status == 'DA' && validator.definedVal(isRegisterBankList[0].receiver_id)
            ) {
              beneBankReferData.beneficiaryid = isRegisterBankList[0].receiver_id
              bankCallRequired = false
            }
          }

          if (addAsPending) {
            beneBankReferData.beneficiaryid = tmpbenData.accountnumber
            bankCallRequired = false
          }

          if (handler.BANK_ON_BOARDING_ID == 10) {
            bankCallRequired = false
            beneBankReferData.beneficiaryid = ma_beneficiaries_id
          }

          if (bankCallRequired == true) {
            const apiAPIPayload = {
              ma_user_id: ma_user_id,
              ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID,
              senderid: tmpbenData.remitter_id,
              receivername: tmpbenData.beneficiary_name,
              receivermobilenumber: tmpbenData.ben_mobile_number,
              receiveremailid: '',
              bank: tmpbenData.bank,
              state: tmpbenData.state,
              city: tmpbenData.city,
              branch: tmpbenData.branch,
              address: tmpbenData.address,
              ifscode: tmpbenData.ifscode,
              accountnumber: tmpbenData.accountnumber,
              flag: 2, // IMPS in case NSDL
              bcpartnerrefno: tmpbenData.uic,
              beneficiaryId: ma_beneficiaries_id,
              sendermobilenumber: tmpbenData.sendermobilenumber,
              otp: otp
            }
            let extraPayLoad = {}

            if (isOtpRequired) {
              const beneId = await this.getOrderResponse({ mobile_number: tmpbenData.sendermobilenumber, orderid })
              if (beneId.status == 400) {
                return beneId
              }
              extraPayLoad = { ...beneId.data }
            }

            /**
            * Load Bank Modules
            */

            const Payment = require('./../bankHandler/payment')
            try {
              const payment = new Payment(handler.BANK_NAME, handler.BANK_TPYE, connection, handler.BANK_ON_BOARDING_ID)
              const resRemit = await payment.requestToBank('ADD_BENEFICIARY', { ...apiAPIPayload, ...extraPayLoad }, sessionRQ, connection)

              log.logger({ pagename: require('path').basename(__filename), action: 'isBeneRegister', type: 'beneBankReferData', fields: beneBankReferData })
              // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'isBeneRegister', type: 'beneBankReferData', fields: beneBankReferData })

              if (resRemit.status == 200) {
                // uic, ma_beneficiaries_id, ma_bank_on_boarding_id, conn
                beneBankReferData.beneficiaryid = resRemit.beneficiaryid
              } else {
                return resRemit
              }
            } catch (error) {
              console.log('Bank Module Load Error', error)
              return { status: 400, message: errorMsg.responseCode[1086], respcode: 1086 }
            }
          }

          log.logger({ pagename: require('path').basename(__filename), action: 'isBeneRegister', type: 'beneBankReferData', fields: beneBankReferData })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'isBeneRegister', type: 'beneBankReferData', fields: beneBankReferData })

          if (Object.keys(beneBankReferData).length > 0) {
            if (isRegisterBankList.length > 0) {
              const dataUpdate = { bank_status: 'S', receiver_id: beneBankReferData.beneficiaryid }
              const updateOn = await this.updateWhereData(connection, {
                data: dataUpdate,
                id: isRegisterBankList[0].ma_beneficiary_bank_mapping_id,
                where: 'ma_beneficiary_bank_mapping_id'
              })

              log.logger({ pagename: require('path').basename(__filename), action: 'isBeneRegister', type: 'BeneALreadyExistUpdated', fields: updateOn })
              // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'isBeneRegister', type: 'BeneALreadyExistUpdated', fields: updateOn })
            } else {
              const BBM = await this.bankBeneficiaryMapping(
                {
                  uic: tmpbenData.uic,
                  ma_beneficiaries_id: ma_beneficiaries_id,
                  bank_on_boarding_id: handler.BANK_ON_BOARDING_ID,
                  ma_user_id: ma_user_id,
                  userid: user_id,
                  receiver_id: beneBankReferData.beneficiaryid,
                  bank_status: addAsPending ? 'P' : 'S'
                }, connection)
              if (BBM.status === 400) {
                return BBM
              }
            }

            await this.sendSMSAddBene({
              ma_beneficiaries_id: ma_beneficiaries_id,
              bank_name: handler.BANK_NAME
            }, connection)

            return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, bankName: handler.BANK_NAME }
          } else {
            return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, bankName: handler.BANK_NAME }
          }
        } else {
          return beneficiaryDetailData
        }
      } else { // check if routing failed return error
        return { status: 400, respcode: 1071, message: errorMsg.responseCode[1071] + ' [ADD_BENE_BANK]' }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryBank', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'addBeneficiaryBank', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' [ADD_BENE_BANK]' }
    } finally {
      if (tmpConn) connection.release()
    }
  }

  static async bankBeneficiaryMapping (fields, conn = null) {
    log.logger({ pagename: require('path').basename(__filename), action: 'bankBeneficiaryMapping', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'bankBeneficiaryMapping', type: 'request', fields: fields })
    this.TABLE_NAME = 'ma_beneficiary_bank_mapping'
    const tmpConn = !conn
    const connection = conn || await mySQLWrapper.getConnectionFromPool()
    try {
      const _result = await this.insert(connection, {
        data: {
          uic: fields.uic,
          ma_beneficiaries_id: fields.ma_beneficiaries_id,
          ma_bank_on_boarding_id: fields.bank_on_boarding_id,
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          receiver_id: fields.receiver_id,
          bank_status: fields.bank_status
        }
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'bankBeneficiaryMapping', type: 'response', fields: _result })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'bankBeneficiaryMapping', type: 'response', fields: _result })
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'bankBeneficiaryMapping', type: 'catcherror', fields: err })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'bankBeneficiaryMapping', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (tmpConn) connection.release()
    }
  }

  // ------- SEND BENIFICIARY SUCCESS MESSAGE ------
  static async sendSMSAddBene (fields, conn) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sendSMSAddBene', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'sendSMSAddBene', type: 'request', fields: fields })
    const tmpConn = !conn
    const connection = conn || await mySQLWrapper.getConnectionFromPool()

    try {
      // ------- SEND BENIFICIARY SUCCESS MESSAGE ------
      const sql = `SELECT bf.beneficiary_name,
                    bf.account_number,
                    bf.ifsc_code,
                    bf.mobile_number,
                    bf.ben_mobile_number
                  FROM ma_beneficiaries AS bf
                  WHERE bf.ma_beneficiaries_id = ${fields.ma_beneficiaries_id} limit 1`
      const beneSMSData = await this.rawQuery(sql, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'sendSMSAddBene', type: 'queryResp', fields: beneSMSData[0] })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'sendSMSAddBene', type: 'queryResp', fields: beneSMSData[0] })

      if (beneSMSData.length <= 0) {
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002], details: [] }
      }

      const beneSMSDataItem = beneSMSData[0]

      const BankSignature = util.bankNameCommunication[fields.bank_name]
      let message = util.communication.BENFSUCCESS
      message = message.replace('<benf_name>', beneSMSDataItem.beneficiary_name)
      message = message.replace('<Customer>', 'Customer')
      const accNumber = beneSMSDataItem.account_number.replace(/[A-Za-z0-9](?=[A-Za-z0-9]{4})/g, '')
      message = message.replace('<account_number>', accNumber)
      // message = message.replace('<account_number>', beneSMSDataItem.account_number)
      message = message.replace('<ifsc>', beneSMSDataItem.ifsc_code)
      message = message.replace('<Salutation>', util.communication.Signature + BankSignature)
      await sms.sentSmsAsync(message, beneSMSDataItem.mobile_number, util.templateid.BENFSUCCESS)

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], details: [] }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'sendSMSAddBene', type: 'error', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'sendSMSAddBene', type: 'error', fields: error })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + ' [BEN_SMS]' }
    } finally {
      if (tmpConn) connection.release()
    }
  }

  static async getTryWithOtherBankArray (fields, conn) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getTryBankArray', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getTryBankArray', type: 'request', fields: fields })
    const tmpConn = !conn
    const connection = conn || await mySQLWrapper.getConnectionFromPool()
    const { bank_priority_json, skipBankId } = fields
    try {
      if (!validator.definedVal(skipBankId)) {
        log.logger({ pagename: require('path').basename(__filename), action: 'getTryWithOtherBankArray', type: 'skipBankId is empty', fields: { skipBankId } })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getTryWithOtherBankArray', type: 'skipBankId is empty', fields: { skipBankId } })
        return []
      }
      const BankListData = JSON.parse(bank_priority_json) // priority array
      const tryWithOtherBank = getTryBankArray(BankListData, skipBankId) || []
      log.logger({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'tryWithOtherBank', fields: tryWithOtherBank })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'addBeneficiaryV2', type: 'tryWithOtherBank', fields: tryWithOtherBank })

      if (Array.isArray(tryWithOtherBank) && tryWithOtherBank.length > 0) {
        const BankIdsArray = tryWithOtherBank.map((currentItem) => currentItem.ma_bank_on_boarding_id)
        if (BankIdsArray.length > 0) {
          const checkStatusSQL = `
                SELECT
                map.bank_status as bankRegisterStatus,
                map.ma_bank_on_boarding_id
                FROM ma_customer_details_bank_mapping as map
                WHERE map.uic = '${fields.uic}' AND
                map.ma_bank_on_boarding_id = ${BankIdsArray[0]}
                AND bank_status = 'S'
                limit 1
                `
          log.logger({ pagename: require('path').basename(__filename), action: 'getTryWithOtherBankArray', type: 'checkStatusSQL', fields: checkStatusSQL })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getTryWithOtherBankArray', type: 'checkStatusSQL', fields: checkStatusSQL })
          const checkRemitterStatus = await this.rawQuery(checkStatusSQL, connection)

          log.logger({ pagename: require('path').basename(__filename), action: 'getTryWithOtherBankArray', type: 'checkRemitterStatusArr', fields: checkRemitterStatus })
          // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getTryWithOtherBankArray', type: 'checkRemitterStatusArr', fields: checkRemitterStatus })

          if (checkRemitterStatus.length > 0) {
            const nextBankItem = []
            tryWithOtherBank.forEach(bankItem => {
              const bankFound = checkRemitterStatus.find((item) => item.ma_bank_on_boarding_id == bankItem.ma_bank_on_boarding_id)
              if (validator.definedVal(bankFound)) {
                bankItem.bankRegisterStatus = bankFound.bankRegisterStatus
                nextBankItem.push(bankItem)
              } else {
                bankItem.bankRegisterStatus = 'U'
              }
            })

            return nextBankItem
          } else {
            return []
          }
        }
      }

      return tryWithOtherBank
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getTryWithOtherBankArray', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'getTryWithOtherBankArray', type: 'catcherror', fields: error })
      return []
    } finally {
      if (tmpConn) connection.release()
    }
  }

  // static async checkBeneExistAtBank (_, fields) {
  //   log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'request', fields: fields })
  //   // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'request', fields: fields })

  //   if (!validator.definedVal(fields.countrycode)) {
  //     fields.countrycode = 'IN'
  //   }

  //   if (!validator.definedVal(fields.sessionRQ)) {
  //     return { status: 400, respcode: 1161, message: errorMsg.responseCode[1161], action_code: 1001 }
  //   }

  //   if (!validator.definedVal(fields.uic)) {
  //     return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051], action_code: 1001 }
  //   }

  //   const isValidAccountNo = validator.validateAccNoAlphaNumeric(fields.account_number)
  //   if (!isValidAccountNo) {
  //     return { status: 400, respcode: 1089, message: errorMsg.responseCode[1089] + ':account_number ', action_code: 1001 }
  //   }

  //   if (!validator.definedVal(fields.ifsc_code)) {
  //     return { status: 400, respcode: 1031, message: errorMsg.responseCode[1031], action_code: 1001 }
  //   }

  //   const validate = validator.validateMobile(fields.mobile_number, fields.countrycode)
  //   log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'validateMobile', fields: validate })
  //   // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'validateMobile', fields: validate })
  //   if (validate.status === false) {
  //     return { status: 400, respcode: 1033, message: errorMsg.responseCode[1033], action_code: 1001 }
  //   }

  //   const connection = await mySQLWrapper.getConnectionFromPool()
  //   try {
  //     const sessionData = await dmtSession.getDMTSessionData(connection, fields.sessionRQ, {
  //       ma_user_id: fields.ma_user_id,
  //       user_id: fields.userid,
  //       uic: fields.uic,
  //       ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id
  //     })

  //     log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'sessionData', fields: sessionData })
  //     // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'sessionData', fields: sessionData })

  //     if (sessionData.status == 400) {
  //       sessionData.action_code = 1001
  //       return sessionData
  //     }

  //     const handler = sessionData.handler

  //     // IFSC code verification
  //     const verifyIFSC = await bankBranch.validateIfscCode(fields.ifsc_code, connection)
  //     log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'verifyIFSC', fields: verifyIFSC })
  //     // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'verifyIFSC', fields: verifyIFSC })

  //     if (verifyIFSC.status === 400) {
  //       return { status: 400, respcode: 1031, message: errorMsg.responseCode[1031], action_code: 1001 }
  //     }

  //     const bankDetailData = await bankOnBoardDetails.getBankDetailsList(null, { ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID, entity_type: 'CHECK_BENE_EXISTS' }, connection)

  //     log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'bankDetailData', fields: bankDetailData })
  //     // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'bankDetailData', fields: bankDetailData })

  //     const sql = `SELECT mcdbm.remitter_id, mcd.mobile_number FROM ma_customer_details as mcd
  //     JOIN ma_customer_details_bank_mapping as mcdbm on mcdbm.ma_customer_details_id = mcd.ma_customer_details_id
  //     WHERE mcd.uic = '${fields.uic}' AND customer_status = 'Y' AND ma_bank_on_boarding_id = ${handler.BANK_ON_BOARDING_ID} `
  //     const remitterResp = await this.rawQuery(sql, connection)
  //     if (remitterResp.length <= 0) {
  //       return {
  //         status: 400,
  //         respcode: 1115,
  //         message: errorMsg.responseCode[1115] + ' [UIC~' + fields.uic + ']',
  //         action_code: 1001
  //       }
  //     }

  //     const { mobile_number, remitter_id } = remitterResp[0]

  //     let bankOtp = false
  //     if (bankDetailData.status === 200 && bankDetailData.data.length > 0) {
  //       const bankData = bankDetailData.data
  //       if (bankData[0].otp_required === 'YES') {
  //         bankOtp = true
  //       } else {
  //         return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002], action_code: 1000 }
  //       }
  //     } else {
  //       // Invalid bank if data not found in db table ma_bank_on_boarding_details
  //       return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002], action_code: 1000 }
  //     }

  //     if (bankOtp === true) {
  //       const Payment = require('./../bankHandler/payment')

  //       const apiAPIPayload = {
  //         ma_user_id: fields.ma_user_id,
  //         ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID,
  //         senderid: remitter_id,
  //         ifscode: fields.ifsc_code,
  //         accountnumber: fields.account_number,
  //         sendermobilenumber: fields.mobile_number
  //       }

  //       const payment = new Payment(handler.BANK_NAME, handler.BANK_TPYE, connection, handler.BANK_ON_BOARDING_ID)
  //       const resRemit = await payment.requestToBank('VIEW_BENEFICIARY', apiAPIPayload, fields.sessionRQ, connection)

  //       log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'beneBankReferData', fields: resRemit })
  //       // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'beneBankReferData', fields: resRemit })

  //       if (resRemit.status == 200) {
  //         if (resRemit.existWithDiffIfsc == false) {
  //           return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002], action_code: 1000 }
  //         }
  //         // uic, ma_beneficiaries_id, ma_bank_on_boarding_id, conn

  //         // IFSC code verification
  //         if (resRemit.ifscode) {
  //           const verifyIFSC = await bankBranch.validateIfscCode(resRemit.ifscode, connection)
  //           log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'verifyIFSC', fields: verifyIFSC })
  //           // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'verifyIFSC', fields: verifyIFSC })

  //           if (verifyIFSC.status === 400) {
  //             return { status: 400, respcode: 1031, message: errorMsg.responseCode[1031] + '~[' + resRemit.ifscode + ']', action_code: 1001 }
  //           }
  //         }

  //         if (('existWithDiffIfsc' in resRemit) && resRemit.existWithDiffIfsc === true) {
  //           return {
  //             status: 200,
  //             respcode: 1000,
  //             message: errorMsg.responseCode[1000],
  //             beneExistAtBankDetails: {
  //               beneficiary_name: resRemit.beneficiaryname,
  //               account_number: resRemit.accountnumber,
  //               ifsc_code: resRemit.ifscode,
  //               beneficiaryid: resRemit.beneficiaryid,
  //               bank: resRemit.bank,
  //               ma_bank_master_id: verifyIFSC.ma_bank_master_id,
  //               ben_mobile_number: resRemit.ben_mobile_number || ''
  //             },
  //             action_code: 1000
  //           }
  //         } else {
  //           return {
  //             status: 200,
  //             respcode: 1002,
  //             message: errorMsg.responseCode[1000],
  //             beneExistAtBankDetails: {
  //               beneficiary_name: resRemit.beneficiaryname,
  //               account_number: resRemit.accountnumber,
  //               ifsc_code: resRemit.ifscode,
  //               beneficiaryid: resRemit.beneficiaryid,
  //               bank: resRemit.bank,
  //               ma_bank_master_id: verifyIFSC.ma_bank_master_id,
  //               ben_mobile_number: resRemit.ben_mobile_number || ''
  //             },
  //             action_code: 1000
  //           }
  //         }
  //       } else {
  //         resRemit.action_code = 1001
  //         return resRemit
  //       }
  //     }
  //   } catch (error) {
  //     log.logger({ pagename: require('path').basename(__filename), action: 'bankBeneficiaryMapping', type: 'catcherror', fields: error })
  //     // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'bankBeneficiaryMapping', type: 'catcherror', fields: error })
  //     return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
  //   } finally {
  //     connection.release()
  //   }
  // }

  static async checkBeneExistAtBank (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'request', fields: fields })

    if (!validator.definedVal(fields.countrycode)) {
      fields.countrycode = 'IN'
    }

    if (!validator.definedVal(fields.sessionRQ)) {
      return { status: 400, respcode: 1161, message: errorMsg.responseCode[1161], action_code: 1001 }
    }

    if (!validator.definedVal(fields.uic)) {
      return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051], action_code: 1001 }
    }

    const isValidAccountNo = validator.validateAccNoAlphaNumeric(fields.account_number)
    if (!isValidAccountNo) {
      return { status: 400, respcode: 1089, message: errorMsg.responseCode[1089] + ':account_number ', action_code: 1001 }
    }

    if (!validator.definedVal(fields.ifsc_code)) {
      return { status: 400, respcode: 1031, message: errorMsg.responseCode[1031], action_code: 1001 }
    }

    const validate = validator.validateMobile(fields.mobile_number, fields.countrycode)
    log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'validateMobile', fields: validate })
    if (validate.status === false) {
      return { status: 400, respcode: 1033, message: errorMsg.responseCode[1033], action_code: 1001 }
    }

    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sessionData = await dmtSession.getDMTSessionData(connection, fields.sessionRQ, {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid,
        uic: fields.uic,
        ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'sessionData', fields: sessionData })

      if (sessionData.status == 400) {
        sessionData.action_code = 1001
        return sessionData
      }

      const handler = sessionData.handler

      // IFSC code verification
      const verifyIFSC = await bankBranch.validateIfscCode(fields.ifsc_code, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'verifyIFSC', fields: verifyIFSC })

      if (verifyIFSC.status === 400) {
        return { status: 400, respcode: 1031, message: errorMsg.responseCode[1031], action_code: 1001 }
      }

      const bankDetailData = await bankOnBoardDetails.getBankDetailsList(null, { ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID, entity_type: 'CHECK_BENE_EXISTS' }, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'bankDetailData', fields: bankDetailData })

      const sql = `SELECT mcdbm.remitter_id, mcd.mobile_number FROM ma_customer_details as mcd
      JOIN ma_customer_details_bank_mapping as mcdbm on mcdbm.ma_customer_details_id = mcd.ma_customer_details_id
      WHERE mcd.uic = '${fields.uic}' AND customer_status = 'Y' AND ma_bank_on_boarding_id = ${handler.BANK_ON_BOARDING_ID} `
      const remitterResp = await this.rawQuery(sql, connection)
      if (remitterResp.length <= 0) {
        return {
          status: 400,
          respcode: 1115,
          message: errorMsg.responseCode[1115] + ' [UIC~' + fields.uic + ']',
          action_code: 1001
        }
      }

      const { mobile_number, remitter_id } = remitterResp[0]

      let bankOtp = false
      if (bankDetailData.status === 200 && bankDetailData.data.length > 0) {
        const bankData = bankDetailData.data
        if (bankData[0].otp_required === 'YES') {
          bankOtp = true
        } else {
          // return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002], action_code: 1000 }
          const checkBene = `SELECT b.beneficiary_name,b.ben_mobile_number,b.ma_bene_verification_id FROM ma_beneficiaries b LEFT JOIN ma_bene_verification as vrfy ON vrfy.ma_bene_verification_id = b.ma_bene_verification_id WHERE b.account_number = '${fields.account_number}' AND b.ifsc_code = '${fields.ifsc_code}' AND b.uic = '${fields.uic}' AND b.beneficiary_status != 'D' ORDER BY vrfy.addedon DESC LIMIT 1`

          log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'checkBeneficiarySQL', fields: checkBene })

          const beneficiaryDetails = await this.rawQuery(checkBene, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'response', fields: beneficiaryDetails })
          if (beneficiaryDetails.length == 0) {
            if (handler.BANK_ON_BOARDING_ID == 5) {
              return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002] + ' Benefciary' }
            }
            return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002] + ' Benefciary', navigation: 'show_OTP' }
          } else if (beneficiaryDetails.length > 0 && beneficiaryDetails[0].ma_bene_verification_id == 0) {
            if (handler.BANK_ON_BOARDING_ID == 5) {
              return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002] + ' Benefciary' }
            }
            return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002] + ' Benefciary', navigation: 'show_OTP' }
          }
          return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002], action_code: 1000 }
        }
      } else {
        // Invalid bank if data not found in db table ma_bank_on_boarding_details
        return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002], action_code: 1000 }
      }

      if (bankOtp === true) {
        const Payment = require('./../bankHandler/payment')

        const apiAPIPayload = {
          ma_user_id: fields.ma_user_id,
          ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID,
          senderid: remitter_id,
          ifscode: fields.ifsc_code,
          accountnumber: fields.account_number,
          sendermobilenumber: fields.mobile_number
        }

        const payment = new Payment(handler.BANK_NAME, handler.BANK_TPYE, connection, handler.BANK_ON_BOARDING_ID)
        const resRemit = await payment.requestToBank('VIEW_BENEFICIARY', apiAPIPayload, fields.sessionRQ, connection)

        log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'beneBankReferData', fields: resRemit })

        if (resRemit.status == 200) {
          if (resRemit.existWithDiffIfsc == false) {
            return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002], action_code: 1000 }
          }
          // uic, ma_beneficiaries_id, ma_bank_on_boarding_id, conn

          // IFSC code verification
          if (resRemit.ifscode) {
            const verifyIFSC = await bankBranch.validateIfscCode(resRemit.ifscode, connection)
            log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'verifyIFSC', fields: verifyIFSC })

            if (verifyIFSC.status === 400) {
              return { status: 400, respcode: 1031, message: errorMsg.responseCode[1031] + '~[' + resRemit.ifscode + ']', action_code: 1001 }
            }
          }

          if (('existWithDiffIfsc' in resRemit) && resRemit.existWithDiffIfsc === true) {
            return {
              status: 200,
              respcode: 1000,
              message: errorMsg.responseCode[1000],
              beneExistAtBankDetails: {
                beneficiary_name: resRemit.beneficiaryname,
                account_number: resRemit.accountnumber,
                ifsc_code: resRemit.ifscode,
                beneficiaryid: resRemit.beneficiaryid,
                bank: resRemit.bank,
                ma_bank_master_id: verifyIFSC.ma_bank_master_id,
                ben_mobile_number: resRemit.ben_mobile_number || ''
              },
              action_code: 1000
            }
          } else {
            return {
              status: 200,
              respcode: 1002,
              message: errorMsg.responseCode[1000],
              beneExistAtBankDetails: {
                beneficiary_name: resRemit.beneficiaryname,
                account_number: resRemit.accountnumber,
                ifsc_code: resRemit.ifscode,
                beneficiaryid: resRemit.beneficiaryid,
                bank: resRemit.bank,
                ma_bank_master_id: verifyIFSC.ma_bank_master_id,
                ben_mobile_number: resRemit.ben_mobile_number || ''
              },
              action_code: 1000
            }
          }
        } else {
          resRemit.action_code = 1001
          return resRemit
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExistAtBank', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async checkBeneExist (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExist', type: 'request', fields: fields })

    if (!validator.definedVal(fields.countrycode)) {
      fields.countrycode = 'IN'
    }

    if (!validator.definedVal(fields.sessionRQ)) {
      return { status: 400, respcode: 1161, message: errorMsg.responseCode[1161], action_code: 1001 }
    }

    if (!validator.definedVal(fields.uic)) {
      return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051], action_code: 1001 }
    }

    const isValidAccountNo = validator.validateAccNoAlphaNumeric(fields.account_number)
    if (!isValidAccountNo) {
      return { status: 400, respcode: 1089, message: errorMsg.responseCode[1089] + ':account_number ', action_code: 1001 }
    }

    if (!validator.definedVal(fields.ifsc_code)) {
      return { status: 400, respcode: 1031, message: errorMsg.responseCode[1031], action_code: 1001 }
    }

    const validate = validator.validateMobile(fields.mobile_number, fields.countrycode)
    log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExist', type: 'validateMobile', fields: validate })
    if (validate.status === false) {
      return { status: 400, respcode: 1033, message: errorMsg.responseCode[1033], action_code: 1001 }
    }

    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sessionData = await dmtSession.getDMTSessionData(connection, fields.sessionRQ, {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid,
        uic: fields.uic,
        ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExist', type: 'sessionData', fields: sessionData })

      if (sessionData.status == 400) {
        sessionData.action_code = 1001
        return sessionData
      }

      const handler = sessionData.handler

      // IFSC code verification
      const verifyIFSC = await bankBranch.validateIfscCode(fields.ifsc_code, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExist', type: 'verifyIFSC', fields: verifyIFSC })

      if (verifyIFSC.status === 400) {
        return { status: 400, respcode: 1031, message: errorMsg.responseCode[1031], action_code: 1001 }
      }

      const bankDetailData = await bankOnBoardDetails.getBankDetailsList(null, { ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID, entity_type: 'CHECK_BENE_EXISTS' }, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExist', type: 'bankDetailData', fields: bankDetailData })

      const sql = `SELECT mcdbm.remitter_id, mcd.mobile_number FROM ma_customer_details as mcd
      JOIN ma_customer_details_bank_mapping as mcdbm on mcdbm.ma_customer_details_id = mcd.ma_customer_details_id
      WHERE mcd.uic = '${fields.uic}' AND customer_status = 'Y' AND ma_bank_on_boarding_id = ${handler.BANK_ON_BOARDING_ID} `
      const remitterResp = await this.rawQuery(sql, connection)
      if (remitterResp.length <= 0) {
        return {
          status: 400,
          respcode: 1115,
          message: errorMsg.responseCode[1115] + ' [UIC~' + fields.uic + ']',
          action_code: 1001
        }
      }

      const { mobile_number, remitter_id } = remitterResp[0]

      let bankOtp = false
      if (bankDetailData.status === 200 && bankDetailData.data.length > 0) {
        const bankData = bankDetailData.data
        if (bankData[0].otp_required === 'YES') {
          bankOtp = true
        } else {
          // return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002], action_code: 1000 }
          const checkBene = `SELECT ma_beneficiaries_id,beneficiary_status,ben_mobile_number FROM ma_beneficiaries WHERE account_number = '${fields.account_number}' and ifsc_code = '${fields.ifsc_code}' and uic= '${fields.uic}' AND beneficiary_status != 'D' `

          log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExist', type: 'checkBeneficiarySQL', fields: checkBene })

          const beneficiaryDetails = await this.rawQuery(checkBene, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExist', type: 'response', fields: beneficiaryDetails })
          if (beneficiaryDetails.length == 0) {
            return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002] + ' Benefciary', navigation: 'show_OTP' }
          }

          const checkBeneVerify = `SELECT ma_bene_verification_id,bene_verify_status,bank_benename FROM ma_bene_verification WHERE account_number = '${fields.account_number}' and ifsc_code = '${fields.ifsc_code}' and uic= '${fields.uic}' ORDER BY addedon DESC LIMIT 1`
          const beneficiaryVerifyDetails = await this.rawQuery(checkBeneVerify, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExist', type: 'beneficiaryVerifyDetails', fields: beneficiaryVerifyDetails })
          if (beneficiaryVerifyDetails.length > 0 && beneficiaryVerifyDetails[0].bene_verify_status != 'S') {
            if (handler.BANK_ON_BOARDING_ID == 5) {
              return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002] + ' Benefciary' }
            }
            return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], navigation: 'show_OTP' }
          } else {
            if (handler.BANK_ON_BOARDING_ID == 5) {
              return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002] + ' Benefciary' }
            }
            return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002], navigation: 'show_OTP' }
          }
        }
      } else {
        // Invalid bank if data not found in db table ma_bank_on_boarding_details
        return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002], action_code: 1000 }
      }

      if (bankOtp === true) {
        const Payment = require('./../bankHandler/payment')

        const apiAPIPayload = {
          ma_user_id: fields.ma_user_id,
          ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID,
          senderid: remitter_id,
          ifscode: fields.ifsc_code,
          accountnumber: fields.account_number,
          sendermobilenumber: fields.mobile_number
        }

        const payment = new Payment(handler.BANK_NAME, handler.BANK_TPYE, connection, handler.BANK_ON_BOARDING_ID)
        const resRemit = await payment.requestToBank('VIEW_BENEFICIARY', apiAPIPayload, fields.sessionRQ, connection)

        log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExist', type: 'beneBankReferData', fields: resRemit })

        if (resRemit.status == 200) {
          if (resRemit.existWithDiffIfsc == false) {
            return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002], action_code: 1000 }
          }
          // uic, ma_beneficiaries_id, ma_bank_on_boarding_id, conn

          // IFSC code verification
          if (resRemit.ifscode) {
            const verifyIFSC = await bankBranch.validateIfscCode(resRemit.ifscode, connection)
            log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExist', type: 'verifyIFSC', fields: verifyIFSC })

            if (verifyIFSC.status === 400) {
              return { status: 400, respcode: 1031, message: errorMsg.responseCode[1031] + '~[' + resRemit.ifscode + ']', action_code: 1001 }
            }
          }

          if (('existWithDiffIfsc' in resRemit) && resRemit.existWithDiffIfsc === true) {
            return {
              status: 200,
              respcode: 1000,
              message: errorMsg.responseCode[1000],
              beneExistAtBankDetails: {
                beneficiary_name: resRemit.beneficiaryname,
                account_number: resRemit.accountnumber,
                ifsc_code: resRemit.ifscode,
                beneficiaryid: resRemit.beneficiaryid,
                bank: resRemit.bank,
                ma_bank_master_id: verifyIFSC.ma_bank_master_id,
                ben_mobile_number: resRemit.ben_mobile_number || ''
              },
              action_code: 1000
            }
          } else {
            return {
              status: 200,
              respcode: 1002,
              message: errorMsg.responseCode[1000],
              beneExistAtBankDetails: {
                beneficiary_name: resRemit.beneficiaryname,
                account_number: resRemit.accountnumber,
                ifsc_code: resRemit.ifscode,
                beneficiaryid: resRemit.beneficiaryid,
                bank: resRemit.bank,
                ma_bank_master_id: verifyIFSC.ma_bank_master_id,
                ben_mobile_number: resRemit.ben_mobile_number || ''
              },
              action_code: 1000
            }
          }
        } else {
          resRemit.action_code = 1001
          return resRemit
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExist', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  static async deleteBankSideBene (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBankSideBene', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBankSideBene', type: 'request', fields: fields })

    if (!validator.definedVal(fields.countrycode)) {
      fields.countrycode = 'IN'
    }

    if (!validator.definedVal(fields.sessionRQ)) {
      return { status: 400, respcode: 1161, message: errorMsg.responseCode[1161], action_code: 1001 }
    }

    if (!validator.definedVal(fields.uic)) {
      return { status: 400, respcode: 1051, message: errorMsg.responseCode[1051], action_code: 1001 }
    }

    const isValidAccountNo = validator.validateAccNoAlphaNumeric(fields.account_number)
    if (!isValidAccountNo) {
      return { status: 400, respcode: 1089, message: errorMsg.responseCode[1089] + ':account_number ', action_code: 1001 }
    }

    if (!validator.definedVal(fields.beneficiaryid)) {
      return { status: 400, respcode: 1091, message: errorMsg.responseCode[1091] + ' as valid beneficiaryid required', action_code: 1001 }
    }

    if (!validator.definedVal(fields.ifsc_code)) {
      return { status: 400, respcode: 1031, message: errorMsg.responseCode[1031], action_code: 1001 }
    }

    const validate = validator.validateMobile(fields.mobile_number, fields.countrycode)
    log.logger({ pagename: require('path').basename(__filename), action: 'deleteBankSideBene', type: 'validateMobile', fields: validate })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBankSideBene', type: 'validateMobile', fields: validate })
    if (validate.status === false) {
      return { status: 400, respcode: 1033, message: errorMsg.responseCode[1033], action_code: 1001 }
    }

    const connection = await mySQLWrapper.getConnectionFromPool()
    try {
      const sessionData = await dmtSession.getDMTSessionData(connection, fields.sessionRQ, {
        ma_user_id: fields.ma_user_id,
        user_id: fields.userid,
        uic: fields.uic,
        ma_bank_on_boarding_id: fields.ma_bank_on_boarding_id
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBankSideBene', type: 'sessionData', fields: sessionData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBankSideBene', type: 'sessionData', fields: sessionData })

      if (sessionData.status == 400) {
        sessionData.action_code = 1001
        return sessionData
      }

      const handler = sessionData.handler

      // IFSC code verification
      const verifyIFSC = await bankBranch.validateIfscCode(fields.ifsc_code, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBankSideBene', type: 'verifyIFSC', fields: verifyIFSC })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBankSideBene', type: 'verifyIFSC', fields: verifyIFSC })

      if (verifyIFSC.status === 400) {
        return { status: 400, respcode: 1031, message: errorMsg.responseCode[1031], action_code: 1001 }
      }

      const bankDetailData = await bankOnBoardDetails.getBankDetailsList(null, { ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID, entity_type: 'DEL_BENEFICIARY' }, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBankSideBene', type: 'bankDetailData', fields: bankDetailData })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBankSideBene', type: 'bankDetailData', fields: bankDetailData })

      const sql = `SELECT mcdbm.remitter_id, mcd.mobile_number FROM ma_customer_details as mcd
      JOIN ma_customer_details_bank_mapping as mcdbm on mcdbm.ma_customer_details_id = mcd.ma_customer_details_id
      WHERE mcd.uic = '${fields.uic}' AND customer_status = 'Y' AND ma_bank_on_boarding_id = ${handler.BANK_ON_BOARDING_ID} `
      const remitterResp = await this.rawQuery(sql, connection)
      if (remitterResp.length <= 0) {
        return {
          status: 400,
          respcode: 1115,
          message: errorMsg.responseCode[1115] + ' [UIC~' + fields.uic + ']',
          action_code: 1001
        }
      }

      const { mobile_number, remitter_id } = remitterResp[0]

      let bankOtp = false
      if (bankDetailData.status === 200 && bankDetailData.data.length > 0) {
        const bankData = bankDetailData.data
        if (bankData[0].otp_required === 'YES' || bankData[0].otp_required === 'NO') {
          bankOtp = true
        } else {
          return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002], action_code: 1000 }
        }
      } else {
        // Invalid bank if data not found in db table ma_bank_on_boarding_details
        return { status: 200, respcode: 1002, message: errorMsg.responseCode[1002], action_code: 1000 }
      }

      if (bankOtp === true) {
        const Payment = require('./../bankHandler/payment')

        const apiAPIPayload = {
          ma_user_id: fields.ma_user_id,
          ma_bank_on_boarding_id: handler.BANK_ON_BOARDING_ID,
          senderid: remitter_id,
          receiverid: fields.beneficiaryid,
          ifscode: fields.ifsc_code,
          accountnumber: fields.account_number,
          sendermobilenumber: fields.mobile_number,
          bankOTPRsponse: {},
          otp: ''
        }

        const payment = new Payment(handler.BANK_NAME, handler.BANK_TPYE, connection, handler.BANK_ON_BOARDING_ID)
        const resRemit = await payment.requestToBank('DELETE_BENEFICIARY', apiAPIPayload, fields.sessionRQ, connection)

        log.logger({ pagename: require('path').basename(__filename), action: 'deleteBankSideBene', type: 'beneBankReferData', fields: resRemit })
        // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'deleteBankSideBene', type: 'beneBankReferData', fields: resRemit })

        if (resRemit.status == 200) {
          return { status: 200, message: errorMsg.responseCode[2005], respcode: 2005, action_code: 1000 }
        } else {
          resRemit.action_code = 1001
          return resRemit
        }
      }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'deleteBankSideBene', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'deleteBankSideBene', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      connection.release()
    }
  }

  /** This function check Beneficary register at bank side with different ifsc code */
  static async checkBeneExistDeleteExist (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExistDeleteExist', type: 'request', fields: fields })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'checkBeneExistDeleteExist', type: 'request', fields: fields })

    const beneExistResponse = await this.checkBeneExistAtBank('', fields)
    log.logger({ pagename: require('path').basename(__filename), action: 'checkBeneExistDeleteExist', type: 'beneExistResponse', fields: beneExistResponse })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'checkBeneExistDeleteExist', type: 'beneExistResponse', fields: beneExistResponse })
    if (beneExistResponse.status == 200 && beneExistResponse.respcode == 1000) {
      const deleteBankResponse = await this.deleteBankSideBene('', {
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        mobile_number: fields.mobile_number,
        uic: fields.uic,
        sessionRQ: fields.sessionRQ,
        account_number: fields.account_number,
        ifsc_code: fields.ifsc_code,
        beneficiaryid: beneExistResponse.beneExistAtBankDetails.beneficiaryid
      })
      return deleteBankResponse
    } else {
      return beneExistResponse
    }
  }

  static async getOrderResponse ({ mobile_number, orderid }) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getOrderResponse', type: 'request', fields: { mobile_number, orderid } })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getOrderResponse', type: 'request', fields: { mobile_number, orderid } })
    // bank handler
    try {
      const _resent = await otp.getResentData(mobile_number, orderid, 'BE')

      log.logger({ pagename: require('path').basename(__filename), action: 'getResentData', type: 'response', fields: _resent })
      // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'getResentData', type: 'response', fields: _resent })

      if (_resent.status == 400) {
        return _resent
      }

      if (_resent.length == 0) {
        return { status: 400, message: errorMsg.responseCode[1002], respcode: 1002 }
      }
      const _resentData = _resent[0]

      return { status: 200, data: JSON.parse(_resentData.response) }
    } catch (error) {
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    }
  }

  /**
   *
   * @param {Object} param
   * @returns
   */
  static async updateBeneficiaryDetails ({ beneficiaryId, data, connection }) {
    const isSet = (connection === null || connection === undefined)
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    log.logger({ pagename: require('path').basename(__filename), action: 'updateBeneficiaryDetails', type: 'fields', fields: data })
    // lokiLogger.info({ pagename: require('path').basename(__filename), action: 'updateBeneficiaryDetails', type: 'fields', fields: data })
    try {
      this.TABLE_NAME = 'ma_beneficiaries'
      await this.updateWhere(conn, { data, id: beneficiaryId, where: 'ma_beneficiaries_id' })
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'updateBeneficiaryDetails', type: 'catcherror', fields: error })
      // lokiLogger.error({ pagename: require('path').basename(__filename), action: 'updateBeneficiaryDetails', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }
}

module.exports = BeneficiaryBankMap
