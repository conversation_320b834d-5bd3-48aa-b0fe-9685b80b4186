const DAO = require('../../lib/dao')
const util = require('../../util/util')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const common = require('../../util/common')
const commonEnum = require('../../model/commonEnum')
const commonFunction = require('../common/commonFunctionController')
const pointsLedger = require('../creditDebit/pointsLedgerController')
const pointsDetailsController = require('../creditDebit/pointsDetailsController')
const pointsrate = require('../pointsRate/pointsRateController')
const balanceController = require('../balance/balanceController')
const transaction = require('../transaction/transactionController')

class transactionCharges extends DAO {

  static async getOrderId () {
    const random = await this.generateRandom(4)
    const timestamp = await this.getTimestamp('')
    const orderId = `MAWEB${random}${timestamp}`
    return orderId
  }

  static async generateRandom (length) {
    var digits = '0123456789'
    let otp = ''
    for (let i = 0; i < length; i++) {
      otp += digits[Math.floor(Math.random() * 10)]
    }
    return otp
  }

  static async getTimestamp () {
    const today = new Date()
    const timezone = today.getTimezoneOffset()
    console.log('timezone', timezone)
    console.log('timezone type', typeof timezone)
    let dd = today.getDate()
    let mm = today.getMonth() + 1
    const yyyy = today.getFullYear()
    if (dd < 10) {
      dd = `0${dd}`
    }
    if (mm < 10) {
      mm = `0${mm}`
    }
    const timeStamp = Math.floor(Date.now() / 1000)
    return `${timeStamp}`

  }

  static async processQrTransactionCharges (fields, userid, ipnrequest) {
    log.logger({ pagename: require('path').basename(__filename), action: 'processQrTransactionCharges', type: 'request', fields: fields })
    console.log('request - userid >>>>>>>', userid)

    try {
      let isFirstQrTxn = false

      // fetch previous qr transaction of the day of this merchant
      const checkFirstTxnQuery = `SELECT ma_qr_charges_transaction_id FROM ma_qr_charges_transaction WHERE ma_user_id = ${fields.ma_user_id} AND userid = ${userid} AND date(addedon) = CURRENT_DATE() ORDER BY addedon DESC LIMIT 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'processQrTransactionCharges', type: 'check if first qr transaction for today - query', fields: checkFirstTxnQuery })

      const checkFirstTxnResult = await this.rawQuery(checkFirstTxnQuery, fields.connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'processQrTransactionCharges', type: 'check if first qr transaction for today - result', fields: checkFirstTxnResult })
 
      if (checkFirstTxnResult.length == 0) isFirstQrTxn = true
      console.log('is first QR transaction of the day >>>>>>>>', isFirstQrTxn)
      
      if (isFirstQrTxn) {
        fields.parent_id = fields.orderid
        fields.order_id = await this.getOrderId() // assign new order id

        // check configuration level and get slab
        const data = await this.fetchQrDistributionData(fields)
        log.logger({ pagename: require('path').basename(__filename), action: 'processQrTransactionCharges', type: 'slab data for qr charges', fields: data })

        if (data.status != 200) return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }

        // deduct charges for first qr transaction
        if (data[0]?.customer_charges > 0) {
          
          fields.userid = userid
          fields.customer_charges = data[0].customer_charges.toString()
          fields.txnType = commonEnum.txnEnumType.getValue('FIRST_QR_TRANSACTION_CHARGES').value || '92'

          const res = await this.deductQrTransactionCharges(fields, ipnrequest)
          log.logger({ pagename: require('path').basename(__filename), action: 'processQrTransactionCharges', type: 'response on deduction of qr charges', fields: res })

          if (res.status == 200) { 
            // enter it as first qr txn
            const insertTxnQuery = `INSERT INTO ma_qr_charges_transaction (ma_user_id, userid, parent_id, transaction_amount, transaction_type, order_id, customer_charges, remarks) VALUES (${fields.ma_user_id}, ${fields.userid}, '${fields.parent_id}', ${fields.amount}, '${fields.txnType}', '${fields.order_id}', ${data[0].customer_charges}, 'First QR transaction charges of the day')`
            log.logger({ pagename: require('path').basename(__filename), action: 'processQrTransactionCharges', type: 'insert qr transaction - query', fields: insertTxnQuery })
      
            const insertTxnResult = await this.rawQuery(insertTxnQuery, fields.connection)
            log.logger({ pagename: require('path').basename(__filename), action: 'processQrTransactionCharges', type: 'insert qr transaction - result', fields: insertTxnResult })
          }
        }
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'processQrTransactionCharges', type: 'catchError', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async fetchQrDistributionData (fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'fetchQrDistributionData', type: 'request', fields: fields })

    try {
      let ma_user_id = 0
      let state_master_id = 0
      let result = {}
      const checkOtherConfigurationFields = { ...fields }

      const txnMode = 'QR'
      let baseSql = `SELECT * FROM ma_slabwise_distribution_qr_charges WHERE paymode = '${txnMode}' AND record_status = 'Y'`

      // retailer specific
      if (fields.ma_user_id && fields.ma_user_id > 0) {
        ma_user_id = fields.ma_user_id

        const retailerSql = baseSql + ` AND ma_user_id = ${ma_user_id} LIMIT 1`
        log.logger({ pagename: require('path').basename(__filename), action: 'fetchQrDistributionData', type: 'retailer level sql', fields: retailerSql })

        result = await this.rawQuery(retailerSql, fields.connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'fetchQrDistributionData', type: 'retailer level result', fields: result })

        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        }
      }

      // distributor specific
      result = {}

      const getDistributionAdditionalCondition = await commonFunction.checkOtherConfiguration({ sql: baseSql, fields: checkOtherConfigurationFields, connection: fields.connection })
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchQrDistributionData', type: 'distributor level data', fields: getDistributionAdditionalCondition })

      if (getDistributionAdditionalCondition.status == 200) {
        result = getDistributionAdditionalCondition.configurationData
        result.status = 200
        result.respcode = 1000
        result.message = errorMsg.responseCode[1000]
        return result
      }

      // state specific
      ma_user_id = 0
      result = {}

      if (fields.state_master_id && fields.state_master_id > 0) {
        state_master_id = fields.state_master_id

        const stateSql = baseSql + ` AND state_master_id = ${state_master_id} AND ma_user_id = ${ma_user_id} LIMIT 1`
        log.logger({ pagename: require('path').basename(__filename), action: 'fetchQrDistributionData', type: 'state level sql', fields: stateSql })
        
        result = await this.rawQuery(stateSql, fields.connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'fetchQrDistributionData', type: 'state level result', fields: result })

        if (result.length > 0) {
          result.status = 200
          result.respcode = 1000
          result.message = errorMsg.responseCode[1000]
          return result
        }
      }

      // global default
      ma_user_id = 0
      state_master_id = 0
      result = {}

      const globalSql = baseSql + ` AND state_master_id = ${state_master_id} AND ma_user_id = ${ma_user_id} AND ma_dt_sdt_id = 0 LIMIT 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchQrDistributionData', type: 'global level sql', fields: globalSql })

      result = await this.rawQuery(globalSql, fields.connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchQrDistributionData', type: 'global level result', fields: result })

      if (result.length > 0) {
        result.status = 200
        result.respcode = 1000
        result.message = errorMsg.responseCode[1000]
        return result
      }

      return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
    } catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchQrDistributionData', type: 'catchError', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async deductQrTransactionCharges (fields, ipnrequest) {
    log.logger({ pagename: require('path').basename(__filename), action: 'deductQrTransactionCharges', type: 'request', fields: fields })

    try {
      const descObj = await common.getSystemCodesByVal(this, { codeName: 'transaction_type', codeVal: '92' }, fields.connection)
      
      // create txn
      const availableBalance = await balanceController.getWalletBalancesDirect('_', { ma_user_id: fields.ma_user_id, ma_status: 'ACTUAL', balance_flag: 'SUMMARY', connection: fields.connection })
      console.log('1. total points balance', availableBalance)
      if (availableBalance.amount < fields.customer_charges) {
        return { status: 400, respcode: 1005, message: errorMsg.responseCode[1005] }
      }

      const pointsFactorData = await pointsrate.getGlobalPointsRate('_', { connection : fields.connection })
      let commission = 0
      let pointsFactor = 1
      if (pointsFactorData.status !== undefined && pointsFactorData.status === 200) {
        pointsFactor = pointsFactorData.points_value
      }

      var txnObj = {
        aggregator_order_id: fields.order_id,
        aggregator_txn_id: '0',
        transaction_status: 'S',
        amount: fields.customer_charges,
        bank_rrn: '',
        ma_user_id: fields.ma_user_id,
        userid: fields.userid,
        transaction_type: fields.txnType,
        remarks: descObj.code_desc ? descObj.code_desc : 'First QR transaction charges of the day',
        mobile_number: ipnrequest.CUSTOMERPHONE || '',
        commission_amount: commission,
        points_factor: pointsFactor,
        transaction_id: fields.order_id,
        transaction_reason: ipnrequest.MESSAGE
      }
      const createtxn = await transaction.createTransaction('_', txnObj, fields.connection)
      if (createtxn.status === 400) {
        await mySQLWrapper.rollback(fields.connection)
        return createtxn
      }

      const createtxnDet = await transaction.createTransactionDetails({}, {
          ma_transaction_master_id: createtxn.transaction_id,
          ma_user_id: fields.ma_user_id,
          userid: fields.userid,
          transaction_id: fields.order_id,
          bank_name: '',
          bank_rrn: '',
          customer_name: '',
          customer_mobile: '',
          customer_email: '',
          terminalid: '',
          form_data: null || ''
        }, fields.connection)
      if (createtxnDet.status === 400) {
        await mySQLWrapper.rollback(fields.connection)
        return createtxnDet
      }

      // points ledger entry
      
      const pointsDetailsEntries = await balanceController.getWalletBalanceDirect('_', {
        ma_user_id: fields.ma_user_id,
        amount: fields.customer_charges,
        transactionType: '1',
        connection: fields.connection
      })
      console.log("get Balance >>>>>>>>>>>>>>>", pointsDetailsEntries)
      log.logger({ pagename: require('path').basename(__filename), action: 'deductQrTransactionCharges', type: 'pointsDetailsEntries', fields: pointsDetailsEntries })
      if (pointsDetailsEntries.status === 400) return pointsDetailsEntries
      
      const chargePoints = await pointsLedger.createEntry('_', {
        ma_user_id: fields.ma_user_id,
        amount: fields.customer_charges,
        mode: 'dr',
        transaction_type: '92',
        description: descObj.code_desc ? descObj.code_desc : 'First QR transaction charges of the day',
        orderid: fields.order_id,
        userid: fields.userid,
        corresponding_id: util.airpayCommissionId,
        ma_status: 'S',
        connection: fields.connection
      })
      if (chargePoints.status === 400) return chargePoints
      
      for (let i = 0; i < pointsDetailsEntries.details.length; i++) {
        const entry = await pointsDetailsController.createEntry('_', {
          ma_user_id: fields.ma_user_id,
          amount: pointsDetailsEntries.details[i].deductionAmount,
          wallet_type: pointsDetailsEntries.details[i].wallet_type,
          ma_points_ledger_master_id: chargePoints.id,
          orderid: fields.order_id,
          ma_status: 'S',
          connection: fields.connection
        })
        log.logger({ pagename: require('path').basename(__filename), action: 'deductQrTransactionCharges', type: 'PointsDetailsEntries', fields: entry })
        if (entry.status === 400) return entry
      }

      const customerCharge = await pointsLedger.createEntry('_', {
        ma_user_id: util.airpayCommissionId,
        amount: fields.customer_charges,
        mode: 'cr',
        transaction_type: '92',
        description: descObj.code_desc ? descObj.code_desc : 'First QR transaction charges of the day',
        orderid: fields.order_id,
        userid: fields.userid,
        corresponding_id: util.airpayUserId,
        ma_status: 'S',
        connection: fields.connection
      })
      if (customerCharge.status === 400) return customerCharge

      await mySQLWrapper.commit(fields.connection)

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000] }
    }
    catch (error) {
      log.logger({ pagename: require('path').basename(__filename), action: 'deductQrTransactionCharges', type: 'catchError', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }
}

module.exports = transactionCharges
