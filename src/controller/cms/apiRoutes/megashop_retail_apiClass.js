const errorMsg = require('../../../util/error')
const log = require('../../../util/log')
const BaseApiClass = require('./BaseApiClass')
const mySQLWrapper = require('../../../lib/mysqlWrapper')
const util = require('../../../util/util')
const nodemailer = require('nodemailer')
const mailconfig = require('../../../lib/mailConfig')
const common = require('../../../util/common')
const moment = require('moment')
const validator = require('../../../util/validator')
const apiConstants = require('./constants')

class Megashop_Retail extends BaseApiClass {
  /**
   * @description Fetches API Params details for dynamic fields
   * @param {Object} fields (Mandatory) json object
   */
  static async fetchApiParams (fields, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'fetchApiParams', type: 'request', fields })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      // get api_param data from ma_cms_merchant_api_details table .....
      const response = await this.formatParam(fields, connection, 'getCmsMerchantDetails')
      log.logger({ pagename: require('path').basename(__filename), action: 'fetchApiParams', type: 'formatParam response', response })
      return response
    } catch (err) {
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  /**
   * Formats response and returns response
   * @param {Object} fields (Mandatory) request data
   * @param {String} fieldsName (Mandatory) field name as per API
   * @param {Object} customerDetailsSqlData (Optional) Employee Data as per Emp Id
   */
  static async formatParam (fields, con, fieldsName, customerDetailsSqlData = []) {
    log.logger({ pagename: require('path').basename(__filename), action: 'formatParam', type: 'hasFields', fields: fields })

    // Connection....
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      // fetch data for api_param for dynamic fileds....
      const apiDetailsSql = `SELECT param_json
                    FROM ma_cms_merchant_api_details
                    WHERE ma_cms_merchant_on_boarding_id = '${fields.ma_cms_merchant_on_boarding_id}' limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'formatParam', type: 'sql - apiDetailsSql', fields: { apiDetailsSql } })
      const apiDetailsSqlData = await this.rawQuery(apiDetailsSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'formatParam', type: 'sql response - apiDetailsSqlData', fields: apiDetailsSqlData })

      // Check data present or not...
      if (apiDetailsSqlData.length <= 0) return { status: 400, message: `${errorMsg.responseCode[1002]} for Dynamic Fileds`, respcode: 1028 }

      // Validate param_json Data...
      if (!(await validator.definedVal(apiDetailsSqlData[0].param_json))) return { status: 400, message: `${errorMsg.responseCode[1002]} Found For param_json`, respcode: 1028 }

      // Create dynamic fileds response (apiClassResponse)...
      const parametersList = JSON.parse(apiDetailsSqlData[0].param_json)
      const apiClassResponseObj = {}
      const apiClassResponse = []
      let count = 0
      // Combine parameterDummy value and parametersList.[fieldsName] ...
      for (const field in parametersList[fieldsName]) {
        // change the value of 'value' key from parametersList Obj using customerDetailsSqlData Obj ...
        if (customerDetailsSqlData.length > 0) if (field in customerDetailsSqlData[0]) parametersList[fieldsName][field].value = customerDetailsSqlData[0][field]

        // Push obj in apiClassResponse obj
        apiClassResponseObj[field] = { ...apiConstants.parameter_dummy }
        apiClassResponse[count] = Object.assign(apiClassResponseObj[field], parametersList[fieldsName][field])

        // Validation key ...
        if ('validation' in apiClassResponse[count] && (apiClassResponse[count].validation == null || apiClassResponse[count].validation == '' || (Array.isArray(apiClassResponse[count].validation) && apiClassResponse[count].validation.length < 1))) {
          apiClassResponse[count].validation = {}
        }
        count++
      }
      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], apiParams: apiClassResponse }
    } catch (err) {
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  /**
   * @description Fetches data base on agent_code for dynamic fields
   * @param {Object} (Mandatory) fields
  */
  static async getAmount (fields, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getAmount', type: 'request', fields })
    // Create connection ...
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()

    // Validate Form Data (fields.form_data) ...
    const formData = JSON.parse(Buffer.from(fields.form_data, 'base64').toString('ascii'))
    if (typeof (formData.agent_code) == 'undefined' || formData.agent_code == null || formData.agent_code == '') {
      log.logger({ pagename: require('path').basename(__filename), action: 'getAmount', type: 'invalid form_data', fields: fields.form_data })
      return { status: 400, respcode: 1083, message: errorMsg.responseCode[1083] }
    }

    try {
      // Get CMS merchants details
      const merchatDetailsSql = `SELECT parent.ma_cms_merchant_on_boarding_id as partner_id, cms.ma_cms_merchant_on_boarding_id as sub_partner_id 
      FROM ma_cms_merchant_on_boarding cms LEFT JOIN ma_cms_merchant_on_boarding parent ON cms.parent_partner_id = parent.ma_cms_merchant_on_boarding_id 
      WHERE cms.merchant_status = 'Y' AND cms.ma_user_id = '${fields.cms_ma_user_id}' LIMIT 1`

      log.logger({ pagename: require('path').basename(__filename), action: 'sendSuccessEmail', type: 'merchatDetailsSql', fields: merchatDetailsSql })
      const merchatDetailsSqlData = await this.rawQuery(merchatDetailsSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'sendSuccessEmail', type: 'merchatDetailsSqlData response', fields: merchatDetailsSqlData })
      if (merchatDetailsSqlData.length <= 0) return { status: 400, respcode: 1028, message: ' Megashop Retail Private Limited Merchant does not exist' }

      // Fetch Employee Id Data ...
      const customerDetailsSql = `SELECT ma_cms_customer_details_id, name AS agent_name, IF(mobile IS NULL OR mobile = '', 'N/A', mobile) AS agent_mobile, IF(branch_id IS NULL OR branch_id = '', 'N/A', branch_id) AS branch_code, IF(branch_name IS NULL OR branch_name = '', 'N/A', branch_name) AS branch_name, email_id AS branch_manager_email_id, remarks
                    FROM ma_cms_customer_details
                    WHERE record_unique_id = '${formData.agent_code}'
                    AND partner_id = '${merchatDetailsSqlData[0].partner_id}' AND sub_partner_id = '${merchatDetailsSqlData[0].sub_partner_id}'
                    AND status = 'YES' AND remarks IS NULL limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'getAmount', type: 'customerDetailsSql', fields: customerDetailsSql })
      const customerDetailsSqlData = await this.rawQuery(customerDetailsSql, connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'getAmount', type: 'sql response - customerDetailsSqlData', fields: customerDetailsSqlData })
      if (customerDetailsSqlData.length <= 0) return { status: 400, respcode: 1028, message: 'Agent code does not exist' }

      const response = await this.formatParam(fields, connection, 'getCmsAmount', customerDetailsSqlData)
      log.logger({ pagename: require('path').basename(__filename), action: 'getAmount', type: 'formatParam response', response })
      return { ...response, cms_unique_id: formData.agent_code }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getAmount', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      // Release the connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  /**
   * @description For customize Opt msg
   * @param {Object} fields (Mandatory) json object
   */
  static async getOtpMsg (fields, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getOtpMsg', type: 'request', fields })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      // Get CMS merchants details
      const merchatDetailsSql = `SELECT parent.ma_cms_merchant_on_boarding_id as partner_id, cms.ma_cms_merchant_on_boarding_id as sub_partner_id,cms.merchant_legal_name  
      FROM ma_cms_merchant_on_boarding cms LEFT JOIN ma_cms_merchant_on_boarding parent ON cms.parent_partner_id = parent.ma_cms_merchant_on_boarding_id 
      WHERE cms.merchant_status = 'Y' AND cms.ma_user_id = '${fields.cms_ma_user_id}' LIMIT 1`

      log.logger({ pagename: require('path').basename(__filename), action: 'getOtpMsg', type: 'merchatDetailsSql', fields: merchatDetailsSql })
      const merchatDetailsSqlData = await this.rawQuery(merchatDetailsSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getOtpMsg', type: 'merchatDetailsSqlData response', fields: merchatDetailsSqlData })
      if (merchatDetailsSqlData.length <= 0) return { status: 400, respcode: 1028, message: ' Megashop Retail Private Limited Merchant does not exist' }

      // Fetch Customer data for Otp Sms ...
      const sql = `SELECT record_unique_id, mobile, name, branch_name
      FROM ma_cms_customer_details
      WHERE record_unique_id = '${fields.cms_unique_id}'
      AND partner_id = '${merchatDetailsSqlData[0].partner_id}' AND sub_partner_id = '${merchatDetailsSqlData[0].sub_partner_id}'
      AND status = 'YES' limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'getOtpMsg', type: 'sql', fields: sql })
      const merchantDetails = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getOtpMsg', type: 'sql response', fields: merchantDetails })

      // Fetch Agent data for Otp Sms ...
      const companysql = `SELECT company
      FROM ma_user_master
      WHERE profileid = ${fields.ma_user_id} limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'getOtpMsg', type: 'companysql', fields: { companysql } })
      const companyDetails = await this.rawQuery(companysql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getOtpMsg', type: 'companyDetails response', fields: companyDetails })

      // Replace Otp SMS values with above data ...
      // For CMS Info Systems we are using same template as SSFB
      let message = util.communication.CMSMEGASHOPRETAIL
      // if (merchantDetails.length > 0 && companyDetails.length > 0) {
      if (merchantDetails.length > 0) {
        const str = merchatDetailsSqlData[0].merchant_legal_name
        const str2 = str.toLowerCase()
        const str3 = str2.substring(0, 15)
        const partner_name = await this.stringToCamelCase(str3)
        // Replace only extra fields. Don't replace <Customer>, <OTP>, <TIME>, <Salutation>
        message = message.replace('<Customer>', merchantDetails[0].name)
        message = message.replace('<Partner_Name>', partner_name)
        message = message.replace('<minutes>', '3')
        if (await validator.definedVal(message)) return { status: 200, smsMessage: message, data: { mobile: merchantDetails[0].mobile, otp_type: 'CMSINFOSYSTEMS' } }
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'getOtpMsg', type: 'Data not found', fields: fields })
      return { status: 400, smsMessage: null, data: null }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getOtpMsg', type: 'catcherror', fields: err })
      return { status: 400, smsMessage: null, data: null }
    } finally {
      // Release the connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async getReceiptDetailsAndSmsMsg (form_data = {}, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getReceiptDetailsAndSmsMsg', type: 'request', fields: form_data })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      const formData = JSON.parse(Buffer.from(form_data.form_data, 'base64').toString('ascii'))

      // Get CMS merchants details
      const merchatDetailsSql = `SELECT parent.ma_cms_merchant_on_boarding_id as partner_id, cms.ma_cms_merchant_on_boarding_id as sub_partner_id,cms.merchant_legal_name 
      FROM ma_cms_merchant_on_boarding cms LEFT JOIN ma_cms_merchant_on_boarding parent ON cms.parent_partner_id = parent.ma_cms_merchant_on_boarding_id 
      WHERE cms.merchant_status = 'Y' AND cms.ma_user_id = '${form_data.cms_ma_user_id}' LIMIT 1`

      log.logger({ pagename: require('path').basename(__filename), action: 'getReceiptDetailsAndSmsMsg', type: 'merchatDetailsSql', fields: merchatDetailsSql })
      const merchatDetailsSqlData = await this.rawQuery(merchatDetailsSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getReceiptDetailsAndSmsMsg', type: 'merchatDetailsSqlData response', fields: merchatDetailsSqlData })
      if (merchatDetailsSqlData.length <= 0) return { status: 400, respcode: 1028, message: ' Megashop Retail Private Limited Merchant does not exist' }

      // get ma_cms_customer_details data for <Customer>, <Branch_Name>...........
      const customerDetailsSql = `SELECT mobile, name, branch_name
      FROM ma_cms_customer_details
      WHERE record_unique_id = '${formData.agent_code}'
      AND partner_id = '${merchatDetailsSqlData[0].partner_id}' AND sub_partner_id = '${merchatDetailsSqlData[0].sub_partner_id}'
      AND status = 'YES' limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'getReceiptDetailsAndSmsMsg', type: 'sql', fields: customerDetailsSql })
      const customerDetailsSqlData = await this.rawQuery(customerDetailsSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getReceiptDetailsAndSmsMsg', type: 'customerDetailsSqlData', fields: customerDetailsSqlData })
      // get ma_cms_customer_details data for <Customer>, <Branch_Name> Close.....

      // get ma_transaction_master data for <date time>...........
      const addedonSql = `SELECT addedon
      FROM ma_transaction_master 
      WHERE aggregator_order_id = '${form_data.aggregator_order_id}' limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'getReceiptDetailsAndSmsMsg', type: 'addedonsql', fields: addedonSql })
      const addedonDetails = await this.rawQuery(addedonSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getReceiptDetailsAndSmsMsg', type: 'addedonDetails response', fields: addedonDetails })
      // get ma_transaction_master data for <date time> Close.....

      // get ma_user_master data for <Agent_Name>...........
      const companySql = `SELECT company
      FROM ma_user_master
      WHERE profileid = '${form_data.ma_user_id}' limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'getReceiptDetailsAndSmsMsg', type: 'companysql', fields: companySql })
      const companyDetails = await this.rawQuery(companySql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'getReceiptDetailsAndSmsMsg', type: 'companyDetails response', fields: companyDetails })
      // get ma_user_master data for <Agent_Name> Close.....

      // Replace only extra fields. Campare util.communication.CMSCUSTSUCCESS and util.communication.CMSANANYACUSTSUCCESS
      // For CMS Info Systems we are using same template as SSFB
      let message = util.communication.CMSMEGASHOPRETAILCUSTSUCCESS
      message = message.replace('<Employee_Code>', formData.agent_code)
      // if (customerDetailsSqlData.length > 0 && addedonDetails.length > 0 && companyDetails.length > 0) {
      if (customerDetailsSqlData.length > 0 && addedonDetails.length > 0) {
        const str = merchatDetailsSqlData[0].merchant_legal_name
        const str2 = str.toLowerCase()
        const str3 = str2.substring(0, 15)
        const partner_name = await this.stringToCamelCase(str3)

        if (await validator.definedVal(message)) {
          message = message.replace('<Customer>', customerDetailsSqlData[0].name || '')
          message = message.replace('<date time>', moment(addedonDetails[0].addedon).format('DD/MM/YYYY h:mm:ss A'))
          message = message.replace('<Partner_Name>', partner_name || '')
          return { status: 200, smsMessage: message, data: { mobile: customerDetailsSqlData[0].mobile, otp_type: 'CMSINFOSYSTEMSCUSTSUCCESS' } }
        }
      }
      log.logger({ pagename: require('path').basename(__filename), action: 'getReceiptDetailsAndSmsMsg', type: 'Null masg', fields: form_data.form_data })
      return { status: 400, smsMessage: null, data: null }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getReceiptDetailsAndSmsMsg', type: 'catcherror', fields: err })
      return { status: 400, smsMessage: null, data: null }
    } finally {
      // Release the connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  /**
   * @description Send Success Email to Suryoday Branch manager id after successful transaction
   * @param {Object} (Mandatory) fields
  */
  static async sendSuccessEmail (fields, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'sendSuccessEmail', type: 'request', fields })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      // Get CMS merchants details
      const merchatDetailsSql = `SELECT parent.ma_cms_merchant_on_boarding_id as partner_id, cms.ma_cms_merchant_on_boarding_id as sub_partner_id 
      FROM ma_cms_merchant_on_boarding cms LEFT JOIN ma_cms_merchant_on_boarding parent ON cms.parent_partner_id = parent.ma_cms_merchant_on_boarding_id 
      WHERE cms.merchant_status = 'Y' AND cms.ma_user_id = '${fields.cms_ma_user_id}' LIMIT 1`

      log.logger({ pagename: require('path').basename(__filename), action: 'sendSuccessEmail', type: 'merchatDetailsSql', fields: merchatDetailsSql })
      const merchatDetailsSqlData = await this.rawQuery(merchatDetailsSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'sendSuccessEmail', type: 'merchatDetailsSqlData response', fields: merchatDetailsSqlData })
      if (merchatDetailsSqlData.length <= 0) return { status: 400, respcode: 1028, message: ' Megashop Retail Private Limited Merchant does not exist' }

      // get ma_cms_customer_details data for email_id...........
      const customerSql = `SELECT email_id, name, branch_name
      FROM ma_cms_customer_details
      WHERE record_unique_id = '${fields.cms_unique_id}'
      AND partner_id = '${merchatDetailsSqlData[0].partner_id}' AND sub_partner_id = '${merchatDetailsSqlData[0].sub_partner_id}'
      AND status = 'YES' limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'sendSuccessEmail', type: 'sql', fields: customerSql })
      const customerSqlData = await this.rawQuery(customerSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'sendSuccessEmail', type: 'sql response', fields: customerSqlData })
      // get ma_cms_customer_details data for email_id...........

      // get ma_transaction_master data for date time...........
      const addedonsql = `SELECT addedon
      FROM ma_transaction_master 
      WHERE aggregator_order_id = '${fields.aggregator_order_id}' limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'sendSuccessEmail', type: 'addedonsql', fields: addedonsql })
      const addedonDetails = await this.rawQuery(addedonsql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'sendSuccessEmail', type: 'addedonDetails response', fields: addedonDetails })
      // get ma_transaction_master data for date time Close.....

      // get ma_user_master data for Agent_Name...........
      const companysql = `SELECT company
      FROM ma_user_master
      WHERE profileid = '${fields.ma_user_id}' limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'sendSuccessEmail', type: 'companysql', fields: companysql })
      const companyDetails = await this.rawQuery(companysql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'sendSuccessEmail', type: 'companyDetails response', fields: companyDetails })
      // get ma_user_master data for Agent Name Close.....

      // Send Mail to CMS Info Systems Branch Manager ...
      const formData = JSON.parse(Buffer.from(fields.form_data, 'base64').toString('ascii'))
      const htmlMessage = `
      <table style="width:600px;margin:0 auto;" align="center">
        <tr>
            <td colspan="3"><img "src="https://retaila.airpay.ninja/img/airpay-logo-vyaapaar.png" height="50"><div style="border:5px solid #0066A6; width:100%;margin-top:15px;"></div></td>
        </tr>
        <tr>
            <td width="55px" align="left" valign="top"><img src="http://airpay.co.in/resources/mailer-img/bigarrow1.png" height="50"></td>
            <td width="490px">
                <div style="min-height:100px;font-family:arial;font-size:12px;line-height:16px;">
                    <p>Dear Sir/Madam,</p>
                    <p> 
                      ${formData.agent_name} with Employee Code - ${formData.agent_code} has deposited an amount of INR ${fields.amount} on ${(customerSqlData.length > 0 && addedonDetails.length > 0 && companyDetails.length > 0) ? moment(addedonDetails[0].addedon).format('DD/MM/YYYY h:mm:ss A') : common.getCurrentDate1()}.
                    </p>
                    <p style="margin-bottom:0;">Transaction ID : ${fields.aggregator_order_id}</p>
                    <p style="margin : 0; padding-top:0;">Branch Name : ${(customerSqlData.length > 0 && addedonDetails.length > 0 && companyDetails.length > 0) ? customerSqlData[0].branch_name : ''}</p>
                    <p style="margin : 0; padding-top:0;">Agent Name : ${(customerSqlData.length > 0 && addedonDetails.length > 0 && companyDetails.length > 0) ? companyDetails[0].company : ''}</p>

                    <p style="margin-bottom:0;">Thank You !!</p>
                    <p style="margin : 0; padding-top:0;">Team Airpay Vyaapaar</p>
                </div>
            </td>
            <td width="55px" align="right" valign="bottom"><img src="http://airpay.co.in/resources/mailer-img/bigarrow2.png"  height="50"></td>
        </tr>
        <tr>
            <td colspan="3"><div style="border:5px solid #0066A6; width:100%;margin-top:15px;"></div></td>
        </tr>
      </table>
      `
      const transporter = nodemailer.createTransport(mailconfig)
      transporter.sendMail({
        from: '<<EMAIL>>', // sender address
        to: customerSqlData[0].email_id, // Branch Manager Mail Id
        subject: ` Megashop Retail Private Limited Transaction - ${fields.aggregator_order_id}`, // Subject line
        html: htmlMessage
      })
      log.logger({ pagename: require('path').basename(__filename), action: 'sendSuccessEmail', type: ' Megashop Retail Private Limited success Email msg', fields: htmlMessage })
      return { status: 200 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'sendSuccessEmail', type: 'catcherror', fields: err })
      return { status: 400 }
    } finally {
      // Release the connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  /**
   * @description For some partners, After incentive entry we need to call WithdrawalRequestAfterIncentive() after receiving a successful response from the third-party API; otherwise we call WithdrawalRequest() before incentive entry.
   * @param {Object} fields (Mandatory)
  */
  static async WithdrawalRequest (fields, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'WithdrawalRequest', type: 'request', fields: fields })
    //
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      const neftDetails = require('../../neftDetails/neftDetailsController')
      fields.withdrawal_transaction_type = '25'
      fields.bulk_settlement = '1'
      const cmsNeftProcess = await neftDetails.cmsNeftProcess(fields, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'WithdrawalRequest', type: 'cmsNeftProcess response', fields: cmsNeftProcess })
      if (cmsNeftProcess.status == 200) {
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
      }
      return cmsNeftProcess
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'WithdrawalRequest', type: 'catcherror', fields: err })
      return { status: 400 }
    } finally {
      // Release the connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  /**
   * @description Calculating employee's(emp_id OR cms_unique_id) todays total successful transaction amount
   * @param {Object} fields (Mandatory)
  */

  static async todaysTotalTransactionAmt (fields, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'todaysTotalTransactionAmt', type: 'request', fields })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      // Sum todays total transaction amount ...
      const totalAmountSql = `SELECT td.ma_transaction_master_details_id
      FROM ma_transaction_master t
      JOIN ma_transaction_master_details td ON t.ma_transaction_master_id = td.ma_transaction_master_id
      WHERE t.transaction_status = 'S'
      AND t.transaction_type = '24'
      AND td.ma_cms_merchant_on_boarding_id = '${fields.ma_cms_merchant_on_boarding_id}'
      AND DATE(t.addedon) =  CURDATE()
      AND td.cms_unique_id = '${fields.cms_unique_id}' `
      log.logger({ pagename: require('path').basename(__filename), action: 'todaysTotalTransactionAmt', type: 'totalAmountSql', fields: totalAmountSql })
      const totalAmount = await this.rawQuery(totalAmountSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'todaysTotalTransactionAmt', type: 'totalAmountSql response', fields: totalAmount })
      if (totalAmount.length < 5) return { status: 200, message: errorMsg.responseCode[1000], totalAmount }

      return { status: 400, message: 'The maximum transaction limit for the registered agent has been exceeded for the day.', totalAmount: null }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'todaysTotalTransactionAmt', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], totalAmount: null }
    } finally {
      // Release the connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async upateCustomerName (fields, con) {
    log.logger({ pagename: require('path').basename(__filename), action: 'upateCustomerName', type: 'fields', fields: fields })
    const tempConnection = !con
    const connection = con || await mySQLWrapper.getConnectionFromPool()
    try {
      // get ma_transaction_master data for ma_transaction_master_id...........
      const idsql = `SELECT td.ma_transaction_master_details_id
      FROM ma_transaction_master t
      JOIN ma_transaction_master_details td ON t.ma_transaction_master_id = td.ma_transaction_master_id
      WHERE t.aggregator_order_id = '${fields.aggregator_order_id}' limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'upateCustomerName', type: 'idsql', fields: { idsql } })
      const idsqlDetails = await this.rawQuery(idsql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'upateCustomerName', type: 'idsqlDetails response', fields: idsqlDetails })
      // get ma_transaction_master data for ma_transaction_master_id Close.....

      // Get CMS merchants details
      const merchatDetailsSql = `SELECT parent.ma_cms_merchant_on_boarding_id as partner_id, cms.ma_cms_merchant_on_boarding_id as sub_partner_id 
      FROM ma_cms_merchant_on_boarding cms LEFT JOIN ma_cms_merchant_on_boarding parent ON cms.parent_partner_id = parent.ma_cms_merchant_on_boarding_id 
      WHERE cms.merchant_status = 'Y' AND cms.ma_user_id = '${fields.cms_ma_user_id}' LIMIT 1`

      log.logger({ pagename: require('path').basename(__filename), action: 'upateCustomerName', type: 'merchatDetailsSql', fields: merchatDetailsSql })
      const merchatDetailsSqlData = await this.rawQuery(merchatDetailsSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'upateCustomerName', type: 'merchatDetailsSqlData response', fields: merchatDetailsSqlData })
      if (merchatDetailsSqlData.length <= 0) return { status: 400, respcode: 1028, message: ' Megashop Retail Private Limited Merchant does not exist' }

      // get ma_cms_customer_details data for email_id...........
      const customerSql = `SELECT name
        FROM ma_cms_customer_details
        WHERE record_unique_id = '${fields.cms_unique_id}'
        AND partner_id = '${merchatDetailsSqlData[0].partner_id}' AND sub_partner_id = '${merchatDetailsSqlData[0].sub_partner_id}'
        AND status = 'YES' limit 1`
      log.logger({ pagename: require('path').basename(__filename), action: 'upateCustomerName', type: 'sql', fields: customerSql })
      const customerSqlData = await this.rawQuery(customerSql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'upateCustomerName', type: 'sql response', fields: customerSqlData })
      // get ma_cms_customer_details data for email_id...........

      if (idsqlDetails.length > 0) {
        const transactionDetailsController = require('../../transaction/transactionDetailsController')
        const updateRes = await transactionDetailsController.updateWhere(connection, {
          id: idsqlDetails[0].ma_transaction_master_details_id,
          where: 'ma_transaction_master_details_id',
          data: { customer_name: customerSqlData[0].name || null }
        })
        log.logger({ pagename: require('path').basename(__filename), action: 'upateCustomerName', type: 'updateRes', fields: updateRes })
        return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000 }
      }
      return { status: 400, message: errorMsg.responseCode[1012], respcode: 1012 }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'upateCustomerName', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], totalAmount: null }
    } finally {
      // Release the connection
      if (tempConnection) {
        connection.release()
      }
    }
  }

  static async stringToCamelCase (str) {
    log.logger({ pagename: require('path').basename(__filename), action: 'stringToCamelCase', type: 'request', fields: { str_val: str } })

    try {
      var splitStr = str.toLowerCase().split(' ')
      for (var i = 0; i < splitStr.length; i++) {
        // You do not need to check if i is larger than splitStr length, as your for does that for you
        // Assign it back to the array
        splitStr[i] = splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1)
      }
      // Directly return the joined string
      return splitStr.join(' ')
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'stringToCamelCase', type: 'err', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      // if (tempConnection) connection.release()
    }
  }
}

module.exports = Megashop_Retail
