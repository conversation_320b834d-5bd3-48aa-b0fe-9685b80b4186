const DAO = require('../../lib/dao')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')
const validator = require('../../util/validator')
const TransactionController = require('../transaction/transactionController')
const Axios = require('axios')

class pobqrActivationController extends DAO {
  static get TABLE_NAME () {
    return 'ma_pob_form_data'
  }

static async fetchPOBFormData (_, fields) {
  log.logger({ pagename: require('path').basename(__filename), action: 'fetchPOBFormData', type: 'request', fields: { fields } })
  const connection = await mySQLWrapper.getConnectionFromPool()
  const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid'])
  if (validateTransferParamRes.status != 200) return validateTransferParamRes
  try {
    const fetchMerchantDetailssql = `SELECT a.company, a.address, msm.name as state, mcm.name as city, a.pincode 
    FROM ma_user_master AS a 
    JOIN ma_states_master as msm on msm.id = a.state
    JOIN ma_cities_master as mcm on mcm.id = a.city
    WHERE a.profileid = ${fields.ma_user_id} AND a.userid = ${fields.userid} AND a.user_status = 'Y' LIMIT 1`
    log.logger({ pagename: require('path').basename(__filename), action: 'fetchPOBFormData', type: 'sql - query', fields: fetchMerchantDetailssql })
    const fetchMerchantDetailsResult = await this.rawQuery(fetchMerchantDetailssql, connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'fetchPOBFormData', type: 'sql - response', fields: fetchMerchantDetailsResult })
    const responseData = {};
    if(fetchMerchantDetailsResult.length > 0) {
      responseData.shop_name = fetchMerchantDetailsResult[0].company;
      responseData.shop_address = fetchMerchantDetailsResult[0].address;
      responseData.shop_state = fetchMerchantDetailsResult[0].state;
      responseData.shop_city = fetchMerchantDetailsResult[0].city;
      responseData.shop_pincode = fetchMerchantDetailsResult[0].pincode;
      return {
      status: 200,
      respcode: 1000,
      message: errorMsg.responseCode[1000],
      data: responseData,
      action_code: 1000
    }
    } 

    return {
      status: 400,
      respcode: 1002,
      message: errorMsg.responseCode[1002],
      action_code: 1002
    }
  }
  catch (err) {
    log.logger({ pagename: require('path').basename(__filename), action: 'fetchPOBFormData', type: 'catcherror', fields: err })
    return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
  } finally {
    connection.release()
  }
 }

static async getRequestIdForPOB(ma_user_id, userid) {
  const connection = await mySQLWrapper.getConnectionFromPool()
  try {
    const fetchRequestIdQuery = `SELECT ma_pob_form_data_id FROM ${this.TABLE_NAME} WHERE ma_user_id = ${ma_user_id} AND userid = ${userid} ORDER BY ma_pob_form_data_id DESC LIMIT 1`
    log.logger({ pagename: require('path').basename(__filename), action: 'getRequestIdForPOB', type: 'fetchRequestIdQuery - query', fields: fetchRequestIdQuery })
    const fetchRequestIdResult = await this.rawQuery(fetchRequestIdQuery, connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'getRequestIdForPOB', type: 'fetchRequestIdResult - result', fields: fetchRequestIdResult })
    if(fetchRequestIdResult.length > 0) {
      return fetchRequestIdResult[0].ma_pob_form_data_id
    } else {
      return null
    }
  } catch (err) {
    log.logger({ pagename: require('path').basename(__filename), action: 'getRequestIdForPOB', type: 'catcherror', fields: err })
    return null
  } finally {
    connection.release()
  }
}


static async fetchQrCodeForUpi (fields) {
  log.logger({ pagename: require('path').basename(__filename), action: 'fetchQrCodeForUpi', type: 'request', fields:  fields  })
  const connection = await mySQLWrapper.getConnectionFromPool()

  // Request body validation
  const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid'])
  if (validateTransferParamRes.status != 200) return validateTransferParamRes

  // Create params for getQrCodeForUpi API 
  const params = {
    ma_user_id: fields.ma_user_id,
    userid: fields.userid
  }
  try {
    const getQrCodeForUpiResponse = await TransactionController.getQrCodeForUpi(_, params);

    // Hardcoded response for testing
    // const getQrCodeForUpiResponse = {
    //   status: 200,
    //   respcode: 1000,
    //   message: errorMsg.responseCode[1000],
    //   barcode_string: 'upi://pay?pa=aps.m268112@icici&pn=Shiva%20Mobiles&tr=APS26112'
    // }

    // Insert API log
    const insertParams = {
      api_name: 'getQrCodeForUpi',
      request: JSON.stringify(params),
      response: JSON.stringify(getQrCodeForUpiResponse),
      ma_user_id: fields.ma_user_id,
      userid: fields.userid,
    }
    const insertApiLogResponse = await this.insertApiLog(insertParams, connection)
    console.log("insertApiLogResponse", insertApiLogResponse);
    return getQrCodeForUpiResponse;

  } catch(err) {
    log.logger({ pagename: require('path').basename(__filename), action: 'fetchQrCodeForUpi', type: 'catcherror', fields: err })
    return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
  } finally {
    connection.release()
  }
}


static async checkQRStatus (_, fields) {
  log.logger({ pagename: require('path').basename(__filename), action: 'checkQRStatus', type: 'request', fields:  fields  })
  const connection = await mySQLWrapper.getConnectionFromPool()

  // Request body validation
  const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid'])
  if (validateTransferParamRes.status != 200) return validateTransferParamRes
  try {
    const fetchPOBFormDetailsQuery = `select ma_pob_form_data_id, pob_approval_status, activation_status from ${this.TABLE_NAME} where ma_user_id = ${fields.ma_user_id} and userid = ${fields.userid} order by ma_pob_form_data_id desc limit 1`
    log.logger({ pagename: require('path').basename(__filename), action: 'checkQRStatus', type: 'fetchPOBFormDetailsQuery - query', fields: fetchPOBFormDetailsQuery })
    const fetchPOBFormDetailsResult = await this.rawQuery(fetchPOBFormDetailsQuery, connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'checkQRStatus', type: 'fetchPOBFormDetailsResult - result', fields: fetchPOBFormDetailsResult })
    if(fetchPOBFormDetailsResult.length > 0) {
      if(fetchPOBFormDetailsResult[0].pob_approval_status == 'A' && fetchPOBFormDetailsResult[0].activation_status == 'A') {
        const getQRCode = await this.fetchQrCodeForUpi(fields);
        return {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          data: {
            activation_status: 'A',
            qr_code: getQRCode?.barcode_string
          },
          action_code: 1000
        }

        
      } else if(fetchPOBFormDetailsResult[0].pob_approval_status == 'P' && fetchPOBFormDetailsResult[0].activation_status == 'P') {
        return {
          status: 200,
          respcode: 1000,
          message: errorMsg.responseCode[1000],
          data: {
            activation_status: 'P',
            qr_code: null
          },
          action_code: 1000
        }

      } else if(fetchPOBFormDetailsResult[0].pob_approval_status == 'P') {
        const getQRCode = await this.fetchQrCodeForUpi(fields);
        if(getQRCode?.status == 200 && getQRCode?.barcode_string != null) {
          const updatePOBActivationStatus = `
            UPDATE ${this.TABLE_NAME}
            SET activation_status = 'A', pob_approval_status = 'A'
            WHERE ma_user_id = ${fields.ma_user_id}
              AND userid = ${fields.userid}
              AND activation_status != 'A'
            ORDER BY ma_pob_form_data_id DESC
            LIMIT 1
          `;      
          log.logger({ pagename: require('path').basename(__filename), action: 'checkQRStatus', type: 'updatePOBActivationStatus - query', fields: updatePOBActivationStatus })
          const updatePOBActivationStatusResult = await this.rawQuery(updatePOBActivationStatus, connection)
          log.logger({ pagename: require('path').basename(__filename), action: 'checkQRStatus', type: 'updatePOBActivationStatusResult - result', fields: updatePOBActivationStatusResult })
          return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], data: { activation_status: 'A', qr_code: getQRCode?.barcode_string }, action_code: 1000 }
        }
      } else {
        return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], data: { activation_status: 'P', qr_code: null }, action_code: 1000 }
      }
    }

    // fetch the request_id from POB
    const final_request_id = await this.getRequestIdForPOB(fields.ma_user_id, fields.userid);
    let newActivationstatus;
    if(!final_request_id) {
      const finalQRCode = await this.fetchQrCodeForUpi(fields);
      if(finalQRCode?.status != 200 || finalQRCode?.barcode_string == null) {
        const insertNewPOBRequest = `INSERT INTO ${this.TABLE_NAME} (ma_user_id, userid, pob_approval_status, activation_status) VALUES (${fields.ma_user_id}, ${fields.userid}, 'P', 'P')`
        log.logger({ pagename: require('path').basename(__filename), action: 'checkQRStatus', type: 'insertNewPOBRequest - query', fields: insertNewPOBRequest })
        const insertNewPOBRequestResult = await this.rawQuery(insertNewPOBRequest, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'checkQRStatus', type: 'insertNewPOBRequestResult - result', fields: insertNewPOBRequestResult })
        newActivationstatus = 'P'    
      } else {
        const insertNewPOBRequest = `INSERT INTO ${this.TABLE_NAME} (ma_user_id, userid, pob_approval_status, activation_status) VALUES (${fields.ma_user_id}, ${fields.userid}, 'A', 'A')`
        log.logger({ pagename: require('path').basename(__filename), action: 'checkQRStatus', type: 'insertNewPOBRequest - query', fields: insertNewPOBRequest })
        const insertNewPOBRequestResult = await this.rawQuery(insertNewPOBRequest, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'checkQRStatus', type: 'insertNewPOBRequestResult - result', fields: insertNewPOBRequestResult }) 
        newActivationstatus = 'A'    
      }
    }
    return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], data: { activation_status: newActivationstatus, qr_code: null }, action_code: 1000 }
  } catch (err) {
    log.logger({ pagename: require('path').basename(__filename), action: 'checkQRStatus', type: 'catcherror', fields: err })
    return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001], action_code: 1001 }
  } finally {
    connection.release()
  }
}

static async updloadDocument(_,fields){
      log.logger({ pagename: require('path').basename(__filename), action: 'updloadDocument', type: 'request', fields: { fields } })
     const connection =await mySQLWrapper.getConnectionFromPool()
     const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid'])
     if (validateTransferParamRes.status != 200) return validateTransferParamRes
      try{
         const sql = `UPDATE ma_pob_form_data SET selected_business_document='${fields.document}',document_file_path='${fields.path}' WHERE ma_user_id=${fields.ma_user_id} AND userid=${fields.userid}`
        log.logger({ pagename: require('path').basename(__filename), action: 'updloadDocument', type: 'request', fields: sql })
         const data = await this.rawQuery(sql, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'updloadDocument', type: 'response', fields: data })
   if (data.affectedRows > 0){
    const pobStatus=`SELECT pob_approval_status from ma_pob_form_data WHERE ma_user_id=${fields.ma_user_id} AND userid=${fields.userid}`
     log.logger({ pagename: require('path').basename(__filename), action: 'updloadDocument', type: 'request', fields: pobStatus })
         const checkStatus = await this.rawQuery(pobStatus, connection)
        log.logger({ pagename: require('path').basename(__filename), action: 'updloadDocument', type: 'checkStatus', fields: checkStatus })
        const pobApprovalStatus = checkStatus[0].pob_approval_status
        console.log("pobApprovalStatus==",pobApprovalStatus)
      return {
        status: 200,
        respcode: 1000,
        pobApprovalStatus,
        message:'Your UPI QR application has been recieved and is under review. Check the UPI QR sectionn for updates'
      }
   }     
    }
    catch(error){
       log.logger({ pagename: require('path').basename(__filename), action: 'updloadDocument', type: 'catcherror', fields: err })
       return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
}
static async pobQrUpiFormData (_, fields) {
    log.logger({ pagename: require('path').basename(__filename), action: 'pobQrUpiFormData', type: 'request', fields:  { fields }  })
    const connection = await mySQLWrapper.getConnectionFromPool()
    const validateTransferParamRes = validator.validateFields(fields, ['ma_user_id', 'userid','shop_name'])
    if (validateTransferParamRes.status != 200) return validateTransferParamRes
 
    try {
   const sql = `INSERT INTO ma_pob_form_data (
  ma_user_id,
  userid,  
  shop_name,
  company_type,
  type_of_establishment,
  shop_address,
  shop_city,
  shop_district,
  shop_state,
  shop_pincode,
  annual_transaction_volume,
  pob_approval_status,
  activation_status
) VALUES (
  ${fields.ma_user_id},
  ${fields.userid},
  '${fields.shop_name}',
  '${fields.company_type}',
  '${fields.type_of_establishment}',
  '${fields.shop_address}',
  '${fields.shop_city}',
  '${fields.shop_district}',
  '${fields.shop_state}',
  ${fields.shop_pincode},
  ${fields.annual_transaction_volume || 0 },
  'P',
  'P'
)`
      log.logger({ pagename: require('path').basename(__filename), action: 'pobQrUpiFormData',  fields: sql })
      const data = await this.rawQuery(sql, connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'pobQrUpiFormData', type: 'request', fields: data })
      if(data.affectedRows === 1) {
      return {
        status: 200,
        respcode: 1000,
        message: errorMsg.responseCode[1000]
          }
       }
       
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'pobQrUpiFormData', type: 'catcherror', fields: err })
      return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
    } finally {
      connection.release()
    }
  }
 
 
 

static async doApiCall (requestInfo, con) {
  log.logger({ pagename: require('path').basename(__filename), action: 'doApiCall', type: 'request', fields: requestInfo })
  const tempConnection = !con
  const connection = con || await mySQLWrapper.getConnectionFromPool()
  let response

  try {
    switch (requestInfo.method.toLowerCase()) {
      case 'post':
        response = await Axios.post(requestInfo.url, requestInfo.data, requestInfo.config)
        break
      case 'get':
        response = await Axios.get(requestInfo.url, requestInfo.data, requestInfo.config)
        break
      case 'put':
        response = await Axios.put(requestInfo.url, requestInfo.data, requestInfo.config)
        break
      case 'patch':
        response = await Axios.patch(requestInfo.url, requestInfo.data, requestInfo.config)
        break
      case 'delete':
        response = await Axios.delete(requestInfo.url, requestInfo.data, requestInfo.config)
        break
      default:
        return { status: 400, respcode: 1002, message: errorMsg.responseCode[1002] }
    }
    log.logger({ pagename: require('path').basename(__filename), action: 'doApiCall', type: 'response', fields: { data: response.data } })
    const requestParams = { api_name: requestInfo.api_name, url: requestInfo.url, data: requestInfo.data, config: requestInfo.config, method: requestInfo.method }

    // Save the request and response data
    await this.insertApiLog({
      api_name: requestInfo.api_name,
      request: JSON.stringify(requestParams).replace(/'/gm, '\'').replace(/\\/gm, '\\\\'),
      response: JSON.stringify(response.data).replace(/'/gm, '\'').replace(/\\/gm, '\\\\'),
      ma_user_id: requestInfo.ma_user_id,
      userid: requestInfo.userid,
      request_id: requestInfo.request_id
    }, connection)

    return {
      status: response.status,
      respcode: response.status === 200 ? 1000 : 1144,
      message: errorMsg.responseCode[response.status === 200 ? 1000 : 1144],
      apiResponse: response.data
    }
  } catch (error) {
    log.logger({ pagename: require('path').basename(__filename), action: 'doApiCall', type: 'error', fields: error })

    const requestParams = {
      api_name: requestInfo.api_name,
      url: requestInfo.url,
      data: requestInfo.data,
      config: requestInfo.config,
      method: requestInfo.method
    }

    // Handle Axios error with response data
    if (error.response && error.response.status && error.response.data) {
      const { status, data } = error.response
      log.logger({ pagename: require('path').basename(__filename), action: 'doApiCall', type: 'error - status, data', fields: { status, data } })

      try {
        // Save request and error response data
        await this.insertApiLog({
          api_name: requestInfo.api_name,
          request: JSON.stringify(requestParams).replace(/'/g, '\'').replace(/\\/g, '\\\\'),
          response: JSON.stringify(data).replace(/'/g, '\'').replace(/\\/g, '\\\\'),
          ma_user_id: requestInfo.ma_user_id,
          userid: requestInfo.userid,
          request_id: requestInfo.request_id
        }, connection)
      } catch (insertError) {
        log.logger({ pagename: require('path').basename(__filename), action: 'doApiCall', type: 'error - insertError', fields: insertError })
      }

      return {
        status: 400,
        respcode: 1001,
        message: errorMsg.responseCode[1001],
        apiResponse: data
      }
    }

    if (error.isAxiosError && error.code === 'ECONNABORTED') {
      console.log('CONNECTION_TIMEOUT: ' + requestInfo.api_name)
      return { status: 301, respcode: 1145, message: errorMsg.responseCode[1145] }
    }
    return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] }
  } 
  finally {
    if (tempConnection) {
      console.log('connection released')
      connection.release()
    }
  }
}

static async insertApiLog (fields, connection) {
  log.logger({ pagename: require('path').basename(__filename), action: 'insertApiLog', type: 'request', fields: fields })
  fields.request_id = fields.request_id ? fields.request_id : null
  try {
    const insertLogQuery = `INSERT INTO ma_pob_activation_api_log (api_name, request, response, ma_user_id, userid, request_id) VALUES ('${fields.api_name}', '${fields.request}', '${fields.response}', ${fields.ma_user_id}, ${fields.userid}, ${fields.request_id})`
    log.logger({ pagename: require('path').basename(__filename), action: 'insertApiLog', type: 'insert api req resp - query', fields: insertLogQuery })

    const insertLogResult = await this.rawQuery(insertLogQuery, connection)
    log.logger({ pagename: require('path').basename(__filename), action: 'insertApiLog', type: 'insert api req resp - result', fields: insertLogResult })
  } catch (error) {
    log.logger({ pagename: require('path').basename(__filename), action: 'insertApiLog', type: 'error', fields: error })
    return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
  }
}

}

module.exports = pobqrActivationController  
