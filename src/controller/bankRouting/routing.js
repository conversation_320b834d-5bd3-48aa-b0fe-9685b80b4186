/* B=>BANK, T=>TYPE, BT=>BANK+TYPE, BB=>BANK+BENEFICIARY, TB=>TYPE+BENEFICIARY, BTB => BANK_+TYPE+BENEFICIARY */
const DAO = require('../../lib/dao')
// const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')

class Routing extends DAO {
  constructor (_, conn = null) {
    super()
    if (conn === null || conn === undefined) {
      return { status: 400, message: 'DB connection not found' }
    } else {
      this.connection = conn
      this.handler = {
        status: 200,
        message: 'success',
        handler: {
          BANK_NAME: 'NSDL',
          BANK_TPYE: 'IMPS',
          BANK_TYPE_ID: 5,
          BANK_ON_BOARDING_ID: 5
        }
      }
    }
  }

  async getHandler (_, reqDataObj) {
    switch (reqDataObj.transferMode) {
      case 'ADDBENEFICIARY':
        return this.addBeneficiary(reqDataObj)
      /**
       * To do dynamic bank on boarding
       * */
      case 'ADDCUSTOMER':
        return this.addCustomer(reqDataObj)
      case 'ADDCUSTOMER_AEPS':
        return this.addCustomerAeps(reqDataObj)
      case 'TRANSFER':
        return this.transfer(reqDataObj)
      case 'RETRY':
        return this.retry(reqDataObj)
      case 'AUTORETRY':
        return this.autoRetry(reqDataObj)
      default:
        return { status: 400, message: 'Transfer mode not found' }
    }
  }

  async addBeneficiary (req) {
    let handler = null
    if (typeof req.beneficiaryId === 'undefined' || req.beneficiaryId === '') {
      return { status: 400, message: 'beneficiaryId is empty' }
    }
    const objBanksType = await this.getBanksType()
    if (objBanksType.status !== 200) {
      return objBanksType
    }
    const bankPriorityList = await this.getPriorityWiseHandler(req)
    if (bankPriorityList.status !== 200) {
      return bankPriorityList
    }
    const excludeBank = await this.routeAtempts(req)
    if (excludeBank.status === 400) {
      return excludeBank
    }
    var bankTypeId
    if (excludeBank.status === 200) {
      const bankName = {}
      for (const [key, value] of Object.entries(excludeBank.data)) {
        bankName[objBanksType.data[value].BANK_NAME] = objBanksType.data[value].BANK_NAME
      }
      for (const [key, value] of Object.entries(bankPriorityList.data)) {
        if (!(value in excludeBank.data) && !(objBanksType.data[value].BANK_NAME in bankName)) {
          bankTypeId = value
          handler = { BANK_NAME: objBanksType.data[bankTypeId].BANK_NAME, BANK_TPYE: objBanksType.data[bankTypeId].BANK_TYPE, BANK_TYPE_ID: bankTypeId, BANK_ON_BOARDING_ID: objBanksType.data[bankTypeId].BANK_ON_BOARDING_ID }
          break
        }
      }
    } else {
      const firstKey = Object.keys(objBanksType.data)[0]
      bankTypeId = firstKey
      handler = { BANK_NAME: objBanksType.data[bankTypeId].BANK_NAME, BANK_TPYE: objBanksType.data[bankTypeId].BANK_TYPE, BANK_TYPE_ID: bankTypeId, BANK_ON_BOARDING_ID: objBanksType.data[bankTypeId].BANK_ON_BOARDING_ID }
    }

    if (handler !== null) {
      const insertData = { transferId: null, bankTypeId: bankTypeId, beneficiaryId: req.beneficiaryId, transferMode: req.transferMode }
      const pushToRouteAttempt = await this.pushToRouteAttempt(insertData)
      if (pushToRouteAttempt.status === 200) {
        return { status: 200, message: 'success', handler: handler }
      } else {
        return pushToRouteAttempt
      }
    } else {
      // return { status: 400, respCode: 1071, message: errorMsg.responseCode[1071] }
      return this.handler
    }
  }

  async transfer (req) {
    let handler = null
    if (typeof req.beneficiaryId === 'undefined' || req.beneficiaryId === '') {
      return { status: 400, message: 'beneficiaryId is empty' }
    }
    if (typeof req.transferId === 'undefined' || req.transferId === '') {
      return { status: 400, message: 'transferid is empty' }
    }
    const bbankMapping = await this.getBeneficiaryBankMapping(req)
    if (bbankMapping.status !== 200) {
      return bbankMapping
    }
    const objBanksType = await this.getBanksType()
    if (objBanksType.status !== 200) {
      return objBanksType
    }
    req.bBankMappingIds = bbankMapping.data
    const bankPriorityList = await this.getPriorityWiseHandler(req)
    if (bankPriorityList.status !== 200) {
      return bankPriorityList
    }
    const excludeBank = await this.routeAtempts(req)
    if (excludeBank.status === 400) {
      return excludeBank
    }
    var bankTypeId
    if (excludeBank.status === 200) {
      /*
      const bankName = {}
      for (const [key, value] of Object.entries(excludeBank.data)) {
        bankName[objBanksType.data[value].BANK_NAME] = objBanksType.data[value].BANK_NAME
      }
      for (const [key,value] of Object.entries(bankPriorityList.data)) {
        if (!(value in excludeBank.data) && (objBanksType.data[value].BANK_NAME in bankName)) {
          bankTypeId = value
          handler = { BANK_NAME: objBanksType.data[bankTypeId].BANK_NAME, BANK_TPYE: objBanksType.data[bankTypeId].BANK_TYPE }
          break
        }
      }
      */
    } else {
      bankTypeId = bankPriorityList.data[1]
      handler = { BANK_NAME: objBanksType.data[bankTypeId].BANK_NAME, BANK_TPYE: objBanksType.data[bankTypeId].BANK_TYPE, BANK_TYPE_ID: bankTypeId, BANK_ON_BOARDING_ID: objBanksType.data[bankTypeId].BANK_ON_BOARDING_ID }
    }
    if (handler !== null) {
      const insertData = { transferId: req.transferId, bankTypeId: bankTypeId, beneficiaryId: req.beneficiaryId, transferMode: req.transferMode }
      const pushToRouteAttempt = await this.pushToRouteAttempt(insertData)
      if (pushToRouteAttempt.status === 200) {
        return { status: 200, message: 'success', handler: handler }
      } else {
        return pushToRouteAttempt
      }
    } else {
      // return { status: 400, respCode: 1071, message: errorMsg.responseCode[1071] }
      return this.handler
    }
  }

  async autoRetry (req) {
    let handler = null
    if (typeof req.beneficiaryId === 'undefined' || req.beneficiaryId === '') {
      return { status: 400, message: 'beneficiaryId is empty' }
    }
    if (typeof req.transferId === 'undefined' || req.transferId === '') {
      return { status: 400, message: 'transferid is empty' }
    }
    const transferBank = await this.getTransferBank(req)
    if (transferBank.status !== 200) {
      return transferBank
    }
    const objBanksType = await this.getBanksType()
    if (objBanksType.status !== 200) {
      return objBanksType
    }
    req.bBankMappingIds = transferBank.data
    const bankPriorityList = await this.getPriorityWiseHandler(req)
    if (bankPriorityList.status !== 200) {
      return bankPriorityList
    }
    const excludeBank = await this.routeAtempts(req)
    if (excludeBank.status === 400) {
      return excludeBank
    }
    var bankTypeId
    if (excludeBank.status === 200) {
      const bankName = {}
      for (const [key, value] of Object.entries(excludeBank.data)) {
        bankName[objBanksType.data[value].BANK_NAME] = objBanksType.data[value].BANK_NAME
      }
      for (const [key, value] of Object.entries(bankPriorityList.data)) {
        if (!(value in excludeBank.data) && (objBanksType.data[value].BANK_NAME in bankName)) {
          bankTypeId = value
          handler = { BANK_NAME: objBanksType.data[bankTypeId].BANK_NAME, BANK_TPYE: objBanksType.data[bankTypeId].BANK_TYPE, BANK_TYPE_ID: bankTypeId, BANK_ON_BOARDING_ID: objBanksType.data[bankTypeId].BANK_ON_BOARDING_ID }
          break
        }
      }
    } else {
      bankTypeId = bankPriorityList.data[1]
      handler = { BANK_NAME: objBanksType.data[bankTypeId].BANK_NAME, BANK_TPYE: objBanksType.data[bankTypeId].BANK_TYPE, BANK_TYPE_ID: bankTypeId, BANK_ON_BOARDING_ID: objBanksType.data[bankTypeId].BANK_ON_BOARDING_ID }
    }
    if (handler !== null) {
      const insertData = { transferId: req.transferId, bankTypeId: bankTypeId, beneficiaryId: req.beneficiaryId, transferMode: req.transferMode }
      const pushToRouteAttempt = await this.pushToRouteAttempt(insertData)
      if (pushToRouteAttempt.status === 200) {
        return { status: 200, message: 'success', handler: handler }
      } else {
        return pushToRouteAttempt
      }
    } else {
      // return { status: 400, respCode: 1071, message: errorMsg.responseCode[1071] }
      return this.handler
    }
  }

  async retry (req) {
    let handler = null
    if (typeof req.beneficiaryId === 'undefined' || req.beneficiaryId === '') {
      return { status: 400, message: 'beneficiaryId is empty' }
    }
    if (typeof req.transferId === 'undefined' || req.transferId === '') {
      return { status: 400, message: 'transferid is empty' }
    }
    const transferBank = await this.getTransferBank(req)
    if (transferBank.status !== 200) {
      return transferBank
    }
    const objBanksType = await this.getBanksType()
    if (objBanksType.status !== 200) {
      return objBanksType
    }
    const objBanks = await this.getBanks()
    if (objBanks.status !== 200) {
      return objBanks
    }
    const bankPriorityList = await this.getPriorityWiseHandler(req)
    if (bankPriorityList.status !== 200) {
      return bankPriorityList
    }
    const excludeBank = await this.routeAtempts(req)
    if (excludeBank.status === 400) {
      return excludeBank
    }
    var bankTypeId
    if (excludeBank.status === 200) {
      const bankName = {}
      for (const [key, value] of Object.entries(excludeBank.data)) {
        bankName[objBanksType.data[value].BANK_NAME] = objBanksType.data[value].BANK_NAME
      }
      for (const [key, value] of Object.entries(bankPriorityList.data)) {
        if (!(value in excludeBank.data) && (objBanksType.data[value].BANK_NAME in bankName)) {
          bankTypeId = value
          handler = { BANK_NAME: objBanksType.data[bankTypeId].BANK_NAME, BANK_TPYE: objBanksType.data[bankTypeId].BANK_TYPE, BANK_TYPE_ID: bankTypeId, BANK_ON_BOARDING_ID: objBanksType.data[bankTypeId].BANK_ON_BOARDING_ID }
          break
        }
      }
      if (handler === null) {
        for (const [key, value] of Object.entries(bankPriorityList.data)) {
          if (!(value in excludeBank.data) && !(objBanksType.data[value].BANK_NAME in bankName)) {
            bankTypeId = value
            handler = { BANK_NAME: objBanksType.data[bankTypeId].BANK_NAME, BANK_TPYE: objBanksType.data[bankTypeId].BANK_TYPE, BANK_TYPE_ID: bankTypeId, BANK_ON_BOARDING_ID: objBanksType.data[bankTypeId].BANK_ON_BOARDING_ID }
            break
          }
        }
      }
    } else {
      // no route attempts so top bank-banktype from transfer bank
      for (const [key, value] of Object.entries(bankPriorityList.data)) {
        // if (objBanksType.data[value].BANK_NAME === objBanks.data[transferBank.data[1]]) {
        if (objBanksType.data[value].BANK_NAME === objBanks.data[Object.keys(transferBank.data)[0]]) {
          bankTypeId = value
          handler = { BANK_NAME: objBanksType.data[bankTypeId].BANK_NAME, BANK_TPYE: objBanksType.data[bankTypeId].BANK_TYPE, BANK_TYPE_ID: bankTypeId, BANK_ON_BOARDING_ID: objBanksType.data[bankTypeId].BANK_ON_BOARDING_ID }
          break
        }
      }
      /* uncomment to test if bank is inactive */
      if (handler === null) {
        bankTypeId = bankPriorityList.data[1]
        handler = { BANK_NAME: objBanksType.data[bankTypeId].BANK_NAME, BANK_TPYE: objBanksType.data[bankTypeId].BANK_TYPE, BANK_TYPE_ID: bankTypeId, BANK_ON_BOARDING_ID: objBanksType.data[bankTypeId].BANK_ON_BOARDING_ID }
      }
    }
    if (handler !== null) {
      const insertData = { transferId: req.transferId, bankTypeId: bankTypeId, beneficiaryId: req.beneficiaryId, transferMode: req.transferMode }
      const pushToRouteAttempt = await this.pushToRouteAttempt(insertData)
      if (pushToRouteAttempt.status === 200) {
        return { status: 200, message: 'success', handler: handler }
      } else {
        return pushToRouteAttempt
      }
    } else {
      // return { status: 400, respCode: 1071, message: errorMsg.responseCode[1071] }
      return this.handler
    }
  }

  async getPriorityWiseHandler (req) {
    const defaultPriority = await this.defaultPriority(req)
    console.log("defaultPriority--->",defaultPriority)
    return defaultPriority
  }

  async defaultPriority (req) {
    log.logger({ pagename: 'routing.js', action: 'defaultPriority', type: 'request', fields: req })
    try {
      const defaultPriorityResult = await this.getPriorityBankList({ ma_user_id: req.ma_user_id, maBankOnBoardingId: req.maBankOnBoardingId})

      if (defaultPriorityResult.status == 400) {
        return defaultPriorityResult
      }

      const defaultPriority = defaultPriorityResult.defaultPriority

      let banks = []
      const benePriority = []

      const sqlsetting = 'SELECT setting_value  FROM ma_global_setting_dmt WHERE setting_code = "dmt_bank_mode" LIMIT 1'

      const sqlSettingData = await DAO.rawQuery(sqlsetting, this.connection)

      log.logger({ pagename: 'routing.js', action: 'defaultPriority', type: 'sqlsetting', fields: sqlSettingData })

      let bankMode = 'M' // M-Multi mode,S-Single mode
      let limit = ''
      if (sqlSettingData.length > 0) {
        bankMode = sqlSettingData[0].setting_value
        limit = 'LIMIT 1'
      }

      /* EKYC LIMIT CHANGES */

      const bankKYCResult = await this.getBankKycStatus()

      if (bankKYCResult.status == 400) {
        return bankKYCResult
      }

      const kycBankLimit = bankKYCResult.kycBankLimit
      /* END EKYC LIMIT CHANGES */

      // If multi mode then return all banks
      defaultPriority.forEach(element => {
        /* add kyc limit */
        element.kyc_limit = kycBankLimit[element.ma_bank_on_boarding_id] ? kycBankLimit[element.ma_bank_on_boarding_id] : kycBankLimit[0]

        banks.push(element)
        benePriority.push({
          bene_priority: element.bene_priority,
          bank_logo: element.bank_logo,
          bank_name: element.bank_name,
          ma_bank_on_boarding_id: element.ma_bank_on_boarding_id,
          bene_verification: element.bene_verification
        })
      })

      if (bankMode == 'S') {
        // If bank mode is Single mode then find the bank which has greater than 100 rs
        const remitterKycStatusResult = await this.getRemitterKycStatus({ defaultPriority, mobile_number: req.mobile_number })
        if (remitterKycStatusResult.status == 400) {
          return remitterKycStatusResult
        }

        const bankKycStatus = remitterKycStatusResult.bankKycStatus
        const bankStatus = remitterKycStatusResult.bankStatus

        const monthlyTransferResult = await this.getRemitterTransferAmountMonthly({ defaultPriority, mobile_number: req.mobile_number })
        if (monthlyTransferResult.status == 400) {
          return monthlyTransferResult
        }

        const transfersTotalMonthly = monthlyTransferResult.transfersTotalMonthly

        let ma_bank_on_boarding = 0
        if (transfersTotalMonthly.length > 0) {
          defaultPriority.forEach(currentBank => {
            const foundBank = transfersTotalMonthly.find((e) => e.ma_bank_on_boarding == currentBank.ma_bank_on_boarding_id) || {}
            currentBank.monthlySum = foundBank.monthlySum || 0
          })

          /* EKYC LIMIT CHANGES */
          // daily consume limit
          const dailyTransferResult = await this.getRemitterTransferAmountDaily({ defaultPriority, mobile_number: req.mobile_number })
          if (dailyTransferResult.status == 400) {
            return dailyTransferResult
          }

          const transfersTotalDaily = dailyTransferResult.transfersTotalDaily

          ma_bank_on_boarding = 0

          defaultPriority.forEach(currentBank => {
            const foundBank = transfersTotalDaily.find((e) => e.ma_bank_on_boarding == currentBank.ma_bank_on_boarding_id) || {}
            currentBank.dailySum = foundBank.dailySum || 0
          })

          /* END EKYC LIMIT CHANGES */

          const sqlLimitsetting = 'SELECT setting_value  from ma_global_setting_dmt where setting_code = "dmt_bank_single_mode_min_limit" limit 1 '

          const sqlLimitSettingData = await DAO.rawQuery(sqlLimitsetting, this.connection)

          log.logger({ pagename: 'routing.js', action: 'defaultPriority', type: 'sqlLimitSettingData', fields: sqlLimitSettingData })

          const singleModeLimit = sqlLimitSettingData.length > 0 ? parseInt(sqlLimitSettingData[0].setting_value) : 100
          // filter out the unqiue bankIDs to reduce API Call
          const isPresent = {}
          const unqiueBankList = []
          for (let index = 0; index < defaultPriority.length; index++) {
            if (!isPresent[defaultPriority[index].ma_bank_on_boarding_id]) {
              unqiueBankList.push(defaultPriority[index])
              isPresent[defaultPriority[index].ma_bank_on_boarding_id] = defaultPriority[index].ma_bank_on_boarding_id
            }
          }
          let bankQueue = []
          for (let index = 0; index < unqiueBankList.length; index++) {
            /* EKYC LIMIT CHANGES */
            const onBoardingBank = unqiueBankList[index]
            const pendingAmt = this.evaluatePendingAmount({ onBoardingBank, bankKycStatus, kycBankLimit })

            /* END EKYC LIMIT CHANGES */
            if (!isNaN(singleModeLimit) && singleModeLimit > 0 && pendingAmt >= singleModeLimit) {
              // check bank's limit
              const bankLimitResponse = await this.getRemitterBalance({
                bankHandler: unqiueBankList[index],
                ma_user_id: req.ma_user_id,
                mobile_number: req.mobile_number,
                max_amount: unqiueBankList[index].max_amount
              })

              log.logger({ pagename: require('path').basename(__filename), action: 'getRemitterBalance', type: 'bankLimitResponse', fields: bankLimitResponse })
              if (bankLimitResponse.status != 200) {
                return bankLimitResponse
              }
              // Check for any mobile number issue with any bank in ma_dmt_bank_fail_list table
              const remitterFailResponse = await this.checkRemitterFailStatus({ma_user_id: req.ma_user_id,mobile_number: req.mobile_number,ma_bank_on_boarding_id:unqiueBankList[index].ma_bank_on_boarding_id})
              console.log(`Onboarding Id ${unqiueBankList[index].ma_bank_on_boarding_id} -- remitterFailResponse ${JSON.stringify(remitterFailResponse)}--->`,)
              console.log("remitterFailResponse?.data?.length == 0--->",remitterFailResponse?.data?.length == 0)
              // if remitterFailResponse is not null, then the remitter mobile no. against the current Bank is deactivated at Bank End.Need to switch to Next Bank
              if (!isNaN(singleModeLimit) &&
                 singleModeLimit > 0 &&
                 bankLimitResponse.remainingLimit > singleModeLimit 
                 && (remitterFailResponse?.data?.length == 0 ) &&
                 bankStatus[unqiueBankList[index].ma_bank_on_boarding_id] && 
                 bankStatus[unqiueBankList[index].ma_bank_on_boarding_id].kyc_status == 'S' &&
                 bankStatus[unqiueBankList[index].ma_bank_on_boarding_id].registration_status == 'S'
              ) {
                ma_bank_on_boarding = unqiueBankList[index].ma_bank_on_boarding_id
                break
              }

              if (!isNaN(singleModeLimit) &&
                 singleModeLimit > 0 &&
                 bankLimitResponse.remainingLimit > singleModeLimit 
                 && (remitterFailResponse?.data?.length == 0 ) ){
                  bankQueue.push(unqiueBankList[index].ma_bank_on_boarding_id)
                 }
            }
          }

          ma_bank_on_boarding = ma_bank_on_boarding > 0 ? ma_bank_on_boarding : bankQueue.shift() // ma_bank_on_boarding if zero pass first bank
        } else {
          ma_bank_on_boarding = defaultPriority[0].ma_bank_on_boarding_id // transfersTotalMonthly is not found then set default first bank
        }

        // reset the bank array
        banks = []
        // filter outs single banks modes
        defaultPriority.forEach(item => {
          if (item.ma_bank_on_boarding_id === ma_bank_on_boarding) {
            banks.push(item)
          }
        })
      }

      return { status: 200, respcode: 1000, message: 'success', data: banks, benePriority: benePriority }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'defaultPriority', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }
  async checkRemitterFailStatus ({  ma_user_id, mobile_number,ma_bank_on_boarding_id }) {
    log.logger({ pagename: 'routing.js', action: 'getRemitterFailList', type: 'request', fields: {  ma_user_id, mobile_number, ma_bank_on_boarding_id } })
    try {
      let remitterFailStatusQuery = `SELECT ma_user_id,userid,mobile_number from ma_dmt_bank_fail_response where mobile_number = '${mobile_number}' and ma_bank_on_boarding_id = ${ma_bank_on_boarding_id} limit 1`
      log.logger({ pagename: 'routing.js', action: 'getRemitterFailList', type: 'remitterFailStatusQuery', fields: remitterFailStatusQuery })
      const remitterFailStatusResp = await DAO.rawQuery(remitterFailStatusQuery, this.connection)
      log.logger({ pagename: 'routing.js', action: 'getRemitterFailList', type: 'remitterFailStatusResp', fields: remitterFailStatusResp })

      if(remitterFailStatusResp.length > 0){
        return { status: 200, respcode: 1000, message: 'Success', data: remitterFailStatusResp }
      }
      return { status: 200, respcode: 1000, message: 'No Record Found', data: remitterFailStatusResp }
    } catch (error) {
      log.logger({ pagename: 'routing.js', action: 'getRemitterFailList', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, data: [] }
    }
  }
  evaluatePendingAmount ({ onBoardingBank, bankKycStatus, kycBankLimit }) {
    const monthlySum = onBoardingBank.monthlySum || 0
    const dailySum = onBoardingBank.dailySum || 0

    let maxAmountDaily = 0
    let maxAmountMonthly = 0
    const currentBankId = onBoardingBank.ma_bank_on_boarding_id

    let remitterBankKycStatus = 'N'
    if (bankKycStatus[currentBankId] &&
      bankKycStatus[currentBankId] == 'S') {
      remitterBankKycStatus = 'Y'
    }
    // set default
    if (!kycBankLimit[currentBankId]) {
      if (remitterBankKycStatus == 'N') {
        maxAmountDaily = kycBankLimit[0].N.per_day_limit
        maxAmountMonthly = kycBankLimit[0].N.per_month_limit
      }

      if (remitterBankKycStatus == 'Y') {
        maxAmountDaily = kycBankLimit[0].Y.per_day_limit
        maxAmountMonthly = kycBankLimit[0].Y.per_month_limit
      }
    }
    // bank level
    if (kycBankLimit[currentBankId]) {
      if (remitterBankKycStatus == 'N') {
        maxAmountDaily = kycBankLimit[currentBankId].N.per_day_limit
        maxAmountMonthly = kycBankLimit[currentBankId].N.per_month_limit
      }

      if (remitterBankKycStatus == 'Y') {
        maxAmountDaily = kycBankLimit[currentBankId].Y.per_day_limit
        maxAmountMonthly = kycBankLimit[currentBankId].Y.per_month_limit
      }
    }

    const pendingAmountDaily = maxAmountDaily - dailySum
    const pendingAmountMonth = maxAmountMonthly - monthlySum

    let pendingAmt = 0
    if (pendingAmountMonth > pendingAmountDaily) {
      pendingAmt = pendingAmountMonth
    } else {
      pendingAmt = pendingAmountDaily
    }
    return pendingAmt
  }

  async getPriorityBankList ({ ma_user_id , maBankOnBoardingId}) {
    try {
      let sql = `SELECT
      b.ma_bank_on_boarding_id,
      b.bank_name,
      b.bank_logo,
      b.priority,
      bt.ma_bank_type_id,
      bt.transaction_type,
      bt.priority,
      b.bene_verification,
      bu.bene_verification as bene_verification_user,
      bu.locationid,
      b.min_amount,
      b.max_amount,
      b.kyc_max_amount,
      b.bank_charges,
      b.bene_priority
      FROM
          ma_bank_on_boarding b
      INNER JOIN 
          ma_user_on_boarding_bank_mapping bu ON
          b.ma_bank_on_boarding_id = bu.ma_bank_on_boarding_id
      INNER JOIN ma_bank_type bt ON
          b.ma_bank_on_boarding_id = bt.ma_bank_on_boarding_id
      WHERE
          b.onboarding_status = 'Y' AND bu.onBoarding_status = 'Y' AND bt.bank_type_status = 'Y'
          AND bu.ma_user_id = ${ma_user_id}
      `
      if (maBankOnBoardingId && maBankOnBoardingId !== undefined) {
      sql += ` AND b.ma_bank_on_boarding_id = ${maBankOnBoardingId}`;
    }

    sql += `
      ORDER BY
        b.priority ASC,
        bt.priority ASC
    `;
      console.log('sql>>', sql)
      const defaultPriority = await DAO.rawQuery(sql, this.connection)
      log.logger({ pagename: 'routing.js', action: 'defaultPriority', type: 'response', fields: defaultPriority })

      if (defaultPriority.length == 0) {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + '~[BNK~EMTY]' }
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], defaultPriority }
    } catch (error) {
      log.logger({ pagename: 'routing.js', action: 'defaultPriority', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  async getBankKycStatus () {
    try {
      // Check KYC limits as per Customer's KYC status
      const kycLimitsSql = 'SELECT per_day_limit,per_month_limit,ma_bank_on_boarding_id,kyc_flag FROM ma_kyc_limits'
      const kycLimits = await DAO.rawQuery(kycLimitsSql, this.connection)
      log.logger({ pagename: require('path').basename(__filename), action: 'kycLimits', type: 'kycLimits', fields: kycLimits })

      if (kycLimits.length <= 0) {
        return { status: 400, message: errorMsg.responseCode[1002] + ': KYC Limits missing', respcode: 1002 }
      }

      const kycBankLimit = {}
      kycLimits.forEach(bank => {
        if (!kycBankLimit[bank.ma_bank_on_boarding_id]) {
          kycBankLimit[bank.ma_bank_on_boarding_id] = {}
        }
        kycBankLimit[bank.ma_bank_on_boarding_id][bank.kyc_flag] = bank
      })

      log.logger({ pagename: require('path').basename(__filename), action: 'kycBankLimit', type: 'kycBankLimitObject', fields: kycBankLimit })

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, kycBankLimit }
    } catch (error) {
      log.logger({ pagename: 'routing.js', action: 'defaultPriority', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  async getRemitterKycStatus ({ defaultPriority, mobile_number }) {
    try {
      const bankIds = []
      const bankKycStatus = {}
      const bankStatus = {}
      defaultPriority.forEach(bank => {
        if (!bankIds.includes(bank.ma_bank_on_boarding_id)) {
          bankIds.push(bank.ma_bank_on_boarding_id)
        }
      })
      const remitterKycStatusQuery = `SELECT kyc_status ,registration_status ,ma_bank_on_boarding_id FROM ma_customer_details_bank_mapping WHERE mobile_number = "${mobile_number}" and ma_bank_on_boarding_id IN (${bankIds.join(',')})`

      log.logger({ pagename: require('path').basename(__filename), action: 'remitterKycStatus', type: 'remitterKycStatusQuery', fields: remitterKycStatusQuery })

      const remitterKycStatus = await DAO.rawQuery(remitterKycStatusQuery, this.connection)

      remitterKycStatus.forEach(kycStatus => {
        bankKycStatus[kycStatus.ma_bank_on_boarding_id] = kycStatus.kyc_status
        bankStatus[kycStatus.ma_bank_on_boarding_id] = kycStatus
      })
      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, bankKycStatus, bankStatus }
    } catch (error) {
      log.logger({ pagename: 'routing.js', action: 'defaultPriority', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  async getRemitterTransferAmountMonthly ({ mobile_number, defaultPriority }) {
    try {
      const today = new Date()
      let cMonth = today.getMonth() + 1
      if (cMonth < 10) {
        cMonth = `0${cMonth}`
      }
      var cDay = today.getDate()
      if (cDay < 10) {
        cDay = `0${cDay}`
      }
      const priorDate = new Date(today.getFullYear(), cMonth, 0) // get last day of the month
      var cMonth_n = priorDate.getMonth() + 1
      if (cMonth_n < 10) {
        cMonth_n = `0${cMonth_n}`
      }
      const priorDateFormat = `${priorDate.getFullYear()}-${cMonth_n}-${priorDate.getDate()}`
      const firstDateFormat = `${priorDate.getFullYear()}-${cMonth_n}-01`
      const sqlMonthly = ` SELECT sum(transfer_amount) as monthlySum,ma_bank_on_boarding FROM ma_transfers where mobile_number ='${mobile_number}' and addedon>='${firstDateFormat} 00:00:00' and addedon<='${priorDateFormat} 23:59:59' and transfer_status in ('S','P','I') group by ma_bank_on_boarding`

      log.logger({ pagename: require('path').basename(__filename), action: 'kycLimits', type: 'sqlMonthlykycLimitsSQL', fields: sqlMonthly })

      const transfersTotalMonthlyResult = await DAO.rawQuery(sqlMonthly, this.connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'kycLimits', type: 'sqlMonthlykycLimitsData', fields: transfersTotalMonthlyResult })

      let transfersTotalMonthly = transfersTotalMonthlyResult
      if (transfersTotalMonthlyResult.length == 0) {
        transfersTotalMonthly = defaultPriority.map(bank => ({ monthlySum: 0, ma_bank_on_boarding_id: bank.ma_bank_on_boarding_id }))
      }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, transfersTotalMonthly }
    } catch (error) {
      log.logger({ pagename: 'routing.js', action: 'defaultPriority', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  async getRemitterTransferAmountDaily ({ mobile_number, defaultPriority }) {
    try {
      const today = new Date()

      const todayDateFormat = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`
      const sqlDaily = ` SELECT sum(transfer_amount) as dailySum,ma_bank_on_boarding FROM ma_transfers where mobile_number ='${mobile_number}' and addedon>='${todayDateFormat} 00:00:00' and addedon<='${todayDateFormat} 23:59:59' and transfer_status in ('S','P','I') group by ma_bank_on_boarding`

      log.logger({ pagename: require('path').basename(__filename), action: 'kycLimits', type: 'sqlMonthlykycLimitsSQL', fields: sqlDaily })

      const transfersTotalDailyResult = await DAO.rawQuery(sqlDaily, this.connection)

      log.logger({ pagename: require('path').basename(__filename), action: 'kycLimits', type: 'sqlDailykycLimitsData', fields: transfersTotalDailyResult })

      let transfersTotalDaily = transfersTotalDailyResult
      if (transfersTotalDailyResult.length == 0) {
        transfersTotalDaily = defaultPriority.map(bank => ({ dailySum: 0, ma_bank_on_boarding_id: bank.ma_bank_on_boarding_id }))
      }

      return { status: 200, message: errorMsg.responseCode[1000], respcode: 1000, transfersTotalDaily }
    } catch (error) {
      log.logger({ pagename: 'routing.js', action: 'defaultPriority', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  async successRatePriority (req) {
    log.logger({ pagename: 'routing.js', action: 'successRatePriority', type: 'request', fields: req })
    const ptCondition = 'DAILY'
    const pbCondition = 'BANK_BANKTYPE'
    try {
      let whereCondition = ` WHERE b.onboarding_status='Y' AND transaction_period='${ptCondition}' `
      if (typeof req.bBankMappingIds !== 'undefined' && Object.keys(req.bBankMappingIds).length > 0) {
        const bbmArr = Object.values(req.bBankMappingIds)
        whereCondition += ' AND b.ma_bank_on_boarding_id IN (' + bbmArr.join(',') + ')'
      }
      let groupBy = ''
      if (pbCondition === 'BANK_BANKTYPE') {
        groupBy = ' GROUP BY b.ma_bank_on_boarding_id,bt.ma_bank_type_id '
      }
      const orderBy = ' ORDER BY success_ratio DESC '
      const sql = 'SELECT b.ma_bank_on_boarding_id,b.bank_name,bt.ma_bank_type_id,bt.transaction_type,bt.priority,b.threshold,sr.total_transactions,sr.failed_transactions,sr.success_transactions,(SUM(sr.success_transactions)/SUM(sr.total_transactions)*100) AS success_ratio,(SUM(sr.failed_transactions)/SUM(sr.total_transactions)*100) AS fail_ratio FROM `ma_transaction_success_rate` sr INNER JOIN ma_bank_type bt ON sr.ma_bank_type_id = bt.ma_bank_type_id INNER JOIN ma_bank_on_boarding b ON bt.ma_bank_on_boarding_id = b.ma_bank_on_boarding_id' + whereCondition + groupBy + orderBy
      const result = await DAO.rawQuery(sql, this.connection)
      log.logger({ pagename: 'routing.js', action: 'successRatePriority', type: 'response', fields: result })
      if (result.length > 0) {
        const resp = {}
        resp.crossedFailThreshold = {}
        let fCounter = 1
        resp.betweenRange = {}
        let bCounter = 1
        result.forEach(element => {
          if (element.fail_ratio > element.threshold) {
            resp.crossedFailThreshold[fCounter] = element.ma_bank_type_id
            fCounter++
          } else {
            resp.betweenRange[bCounter] = element.ma_bank_type_id
            bCounter++
          }
        })
        console.log('crossed failed threshold', resp.crossedFailThreshold)
        return { status: 200, message: 'success', data: resp }
      } else {
        return { status: 201, message: 'no records found' }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'successRatePriority', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  async routeAtempts (req) {
    log.logger({ pagename: 'routing.js', action: 'routeAtempts', type: 'request', fields: req })
    try {
      let where = `transfer_mode = '${req.transferMode}'`
      if (req.transferMode === 'ADDBENEFICIARY') {
        where += ` AND ma_beneficiaries_id = ${req.beneficiaryId}`
      }

      if (req.transferMode === 'ADDCUSTOMER') {
        where += ` AND mobile_number = '${req.mobile_number}'`
      }

      if (req.transferMode === 'TRANSFER' || req.transferMode === 'AUTORETRY' || req.transferMode === 'RETRY') {
        where += ` AND ma_beneficiaries_id = ${req.beneficiaryId} AND ma_transfer_id=${req.transferId}`
      }
      const sql = 'SELECT ma_bank_type_id FROM ma_bank_route_attempt WHERE ' + where
      const result = await DAO.rawQuery(sql, this.connection)
      log.logger({ pagename: 'routing.js', action: 'routeAtempts', type: 'response', fields: result })
      if (result.length > 0) {
        const resp = {}
        result.forEach(element => {
          resp[element.ma_bank_type_id] = element.ma_bank_type_id
        })
        return { status: 200, message: 'success', data: resp }
      } else {
        return { status: 201, message: 'no records found' }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'routeAtempts', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  async getBanks () {
    try {
      const sql = 'SELECT ma_bank_on_boarding_id,bank_name FROM ma_bank_on_boarding where onboarding_status = \'Y\''
      const bankDetails = await DAO.rawQuery(sql, this.connection)
      log.logger({ pagename: 'routing.js', action: 'getBanks', type: 'response', fields: bankDetails })
      if (bankDetails.length > 0) {
        const banks = {}
        bankDetails.forEach(element => {
          banks[element.ma_bank_on_boarding_id] = element.bank_name
        })
        return { status: 200, message: 'success', data: banks }
      } else {
        return { status: 400, message: 'bank records not found,please configure banks' }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getBanks', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  async getBanksType () {
    try {
      const sql = 'SELECT bt.ma_bank_type_id,b.bank_name,b.ma_bank_on_boarding_id,bt.transaction_type FROM ma_bank_on_boarding b INNER JOIN ma_bank_type bt ON b.ma_bank_on_boarding_id = bt.ma_bank_on_boarding_id WHERE b.onboarding_status = \'Y\''
      const bankTypeDetails = await DAO.rawQuery(sql, this.connection)
      log.logger({ pagename: 'routing.js', action: 'getBanksType', type: 'response', fields: bankTypeDetails })
      if (bankTypeDetails.length > 0) {
        const banksType = {}
        bankTypeDetails.forEach(element => {
          const bTypeId = {}
          bTypeId.BANK_NAME = element.bank_name
          bTypeId.BANK_ON_BOARDING_ID = element.ma_bank_on_boarding_id
          bTypeId.BANK_TYPE = element.transaction_type
          banksType[element.ma_bank_type_id] = bTypeId
        })
        return { status: 200, message: 'success', data: banksType }
      } else {
        return { status: 400, message: 'bank records not found, please configure banks' }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getBanksType', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  async pushToRouteAttempt (req) {
    log.logger({ pagename: 'routing.js', action: 'pushToRouteAttempt', type: 'request', fields: req })
    try {
      const sql = `INSERT INTO ma_bank_route_attempt (mobile_number,ma_transfer_id,ma_beneficiaries_id,ma_bank_type_id,transfer_mode) VALUES (${req.mobile_number || null},${req.transferId || null},${req.beneficiaryId || null},${req.bankTypeId || req.bankTypeId},'${req.transferMode}') `
      const result = await DAO.rawQuery(sql, this.connection)
      log.logger({ pagename: 'routing.js', action: 'pushToRouteAttempt', type: 'response', fields: result })
      if (result.insertId) {
        return { status: 200, message: 'success' }
      } else {
        return { status: 400, message: 'no record inserted' }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'pushToRouteAttempt', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  async getBeneficiaryBankMapping (req) {
    log.logger({ pagename: 'routing.js', action: 'getBeneficiaryBankMapping', type: 'request', fields: req })
    try {
      const sql = `SELECT bm.ma_bank_on_boarding_id FROM ma_beneficiaries b INNER JOIN ma_beneficiary_bank_mapping bm ON bm.ma_beneficiaries_id = b.ma_beneficiaries_id WHERE b.beneficiary_status = 'Y' AND b.ma_beneficiaries_id = ${req.beneficiaryId}`
      const result = await DAO.rawQuery(sql, this.connection)
      log.logger({ pagename: 'routing.js', action: 'getBeneficiaryBankMapping', type: 'response', fields: result })
      if (result.length > 0) {
        const resp = {}
        result.forEach(element => {
          resp[element.ma_bank_on_boarding_id] = element.ma_bank_on_boarding_id
        })
        return { status: 200, message: 'success', data: resp }
      } else {
        return { status: 400, message: 'no records found, please configure beneficiary bank mapping' }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getBeneficiaryBankMapping', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  async getTransferBank (req) {
    log.logger({ pagename: 'routing.js', action: 'getTransferBank', type: 'request', fields: req })
    try {
      let where = ' ra.transfer_mode = "TRANSFER" '
      where += ` AND ra.ma_beneficiaries_id = ${req.beneficiaryId} AND ra.ma_transfer_id=${req.transferId}`
      const sql = 'SELECT bt.ma_bank_on_boarding_id FROM ma_bank_type bt INNER JOIN ma_bank_route_attempt ra ON bt.ma_bank_type_id = ra.ma_bank_type_id WHERE ' + where + ' LIMIT 1'
      const result = await DAO.rawQuery(sql, this.connection)
      log.logger({ pagename: 'routing.js', action: 'getTransferBank', type: 'response', fields: result })
      if (result.length > 0) {
        const resp = {}
        result.forEach(element => {
          resp[element.ma_bank_on_boarding_id] = element.ma_bank_on_boarding_id
        })
        return { status: 200, message: 'success', data: resp }
      } else {
        return { status: 400, message: 'no records found, bank handler not found for transfer txn' }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'getTransferBank', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  async addCustomer (req) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addCustomer', type: 'request', fields: req })
    let handler = null
    if (typeof req.mobile_number === 'undefined' || req.mobile_number == null) {
      return { status: 400, respcode: 1001, message: 'mobile_number is empty' }
    }

    const bankPriorityList = await this.getPriorityWiseHandler(req)
    log.logger({ pagename: require('path').basename(__filename), action: 'addCustomer', type: 'bankPriorityList', fields: bankPriorityList })
    if (bankPriorityList.status !== 200) {
      return bankPriorityList
    }

    let priorityData = {}
    let priorityBeneData = {}
    if (bankPriorityList.data.length > 0) {
      const BankListData = bankPriorityList.data
      const BankBeneListData = bankPriorityList.benePriority
      handler = BankListData[0]
      priorityData = BankListData
      priorityBeneData = BankBeneListData
    } else {
      return { status: 400, respcode: 1001, message: 'Bank priority list is empty!' }
    }

    if (handler !== null) {
      const insertData = { mobile_number: req.mobile_number, transferId: null, bankTypeId: handler.ma_bank_type_id, beneficiaryId: req.beneficiaryId, transferMode: req.transferMode }
      await this.pushToRouteAttempt(insertData)
      const handlerData = {
        status: 200,
        message: 'success',
        handler: {
          BANK_NAME: handler.bank_name,
          BANK_TPYE: handler.transaction_type,
          BANK_TYPE_ID: handler.ma_bank_type_id,
          BANK_ON_BOARDING_ID: handler.ma_bank_on_boarding_id
        },
        priorityData: priorityData,
        beneVerify: await this.beneVerify(priorityBeneData)
      }
      return handlerData
    } else {
      // return { status: 400, respCode: 1071, message: errorMsg.responseCode[1071] }
      return this.handler
    }
  }

  async addCustomerAeps (req) {
    log.logger({ pagename: require('path').basename(__filename), action: 'addCustomerAeps', type: 'request', fields: req })
    let handler = null
    if (typeof req.mobile_number === 'undefined' || req.mobile_number == null) {
      return { status: 400, respcode: 1001, message: 'mobile_number is empty' }
    }

    const bankPriorityList = await this.defaultPriorityAeps(req)
    log.logger({ pagename: require('path').basename(__filename), action: 'addCustomerAeps', type: 'bankPriorityList', fields: bankPriorityList })
    if (bankPriorityList.status !== 200) {
      return bankPriorityList
    }

    let priorityData = {}
    if (bankPriorityList.data.length > 0) {
      const BankListData = bankPriorityList.data
      handler = BankListData[0]
      priorityData = BankListData
    } else {
      return { status: 400, respcode: 1001, message: 'Bank priority list is empty!' }
    }

    if (handler !== null) {
      const insertData = { mobile_number: req.mobile_number, transferId: null, bankTypeId: handler.ma_bank_type_id, beneficiaryId: req.beneficiaryId, transferMode: req.transferMode }
      await this.pushToRouteAttempt(insertData)
      const handlerData = {
        status: 200,
        message: 'success',
        handler: {
          BANK_NAME: handler.bank_name,
          BANK_TPYE: handler.transaction_type,
          BANK_TYPE_ID: handler.ma_bank_type_id,
          BANK_ON_BOARDING_ID: handler.ma_bank_on_boarding_id
        },
        priorityData: priorityData
      }
      return handlerData
    } else {
      // return { status: 400, respCode: 1071, message: errorMsg.responseCode[1071] }
      return this.handler
    }
  }

  async defaultPriorityAeps (req) {
    /* SELECT ma_bank_on_boarding_id,bank_name,priority FROM ma_bank_on_boarding ORDER BY priority ASC
         SELECT b.ma_bank_on_boarding_id,b.bank_name,b.priority,bt.ma_bank_type_id,bt.transaction_type,bt.priority FROM ma_bank_on_boarding b INNER JOIN ma_bank_type bt ON b.ma_bank_on_boarding_id = bt.ma_bank_on_boarding_id ORDER BY b.priority,bt.priority ASC
  */
    log.logger({ pagename: 'routing.js', action: 'defaultPriority', type: 'request', fields: req })
    try {
      let whereCondition = ''
      if (typeof req.bBankMappingIds !== 'undefined' && Object.keys(req.bBankMappingIds).length > 0) {
        const bbmArr = Object.values(req.bBankMappingIds)
        whereCondition += ' AND b.ma_bank_on_boarding_id IN (' + bbmArr.join(',') + ')'
      }
      const sql = `SELECT
      b.ma_bank_on_boarding_id,
      b.bank_name,
      b.bank_logo,
      b.priority,
      bt.ma_bank_type_id,
      bt.transaction_type,
      bt.priority,
      bu.bene_verification,
      bu.locationid,
      b.min_amount,
      b.max_amount,
      b.bank_charges,
      b.bene_charges
      FROM
          ma_bank_on_boarding b
      INNER JOIN 
          ma_user_on_boarding_bank_mapping bu ON
          b.ma_bank_on_boarding_id = bu.ma_bank_on_boarding_id
      INNER JOIN ma_bank_type bt ON
          b.ma_bank_on_boarding_id = bt.ma_bank_on_boarding_id
      WHERE
          bu.onBoarding_status = 'Y' AND bt.bank_type_status = 'Y'
          AND bu.ma_user_id = ${req.ma_user_id}
          ${whereCondition}
      ORDER BY
          b.priority ASC,
          bt.priority ASC
      `
      console.log('sql>>', sql)
      const defaultPriority = await DAO.rawQuery(sql, this.connection)
      log.logger({ pagename: 'routing.js', action: 'defaultPriority', type: 'response', fields: defaultPriority })
      if (defaultPriority.length > 0) {
        const banks = []
        defaultPriority.forEach(element => {
          banks.push(element)
        })
        return { status: 200, respcode: 1000, message: 'success', data: banks }
      } else {
        return { status: 400, respcode: 1001, message: errorMsg.responseCode[1001] + '~[BNK~EMTY]' }
      }
    } catch (err) {
      log.logger({ pagename: require('path').basename(__filename), action: 'defaultPriority', type: 'catcherror', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  async beneVerify (priorityData) {
    log.logger({ pagename: require('path').basename(__filename), action: 'beneVerify', type: 'request', fields: priorityData })
    let beneVerifyArr = []
    if (priorityData.length > 0) {
      let beneVerifyBank = priorityData.filter(e => e.bene_verification == 'Y').map((b) => {
        return {
          bank_logo: b.bank_logo,
          bank_name: b.bank_name,
          bene_charges: b.bene_charges,
          bene_priority: b.bene_priority,
          ma_bank_on_boarding_id: b.ma_bank_on_boarding_id
        }
      }
      )

      console.log('beneVerifyBank>>', beneVerifyBank)
      beneVerifyBank = beneVerifyBank.reduce((filter, current) => {
        var dk = filter.find(item => item.ma_bank_on_boarding_id === current.ma_bank_on_boarding_id)
        if (!dk) {
          return filter.concat([current])
        } else {
          return filter
        }
      }, [])

      console.log('beneVerifyBankReduce>>', beneVerifyBank)

      beneVerifyBank.sort((a, b) => parseInt(a.bene_priority) - parseInt(b.bene_priority))

      console.log('beneVerifyBankSort>>', beneVerifyBank)

      beneVerifyArr = beneVerifyBank
    }

    return beneVerifyArr
  }

  async getRemitterBalance ({ bankHandler, ma_user_id, mobile_number, max_amount }) {
    log.logger({ pagename: 'routing.js', action: 'getRemitterBalance', type: 'request', fields: { bankHandler, ma_user_id, mobile_number } })
    try {
      const bankID = bankHandler.ma_bank_on_boarding_id
      const apiAPIPayload = {
        ma_user_id: ma_user_id,
        ma_bank_on_boarding_id: bankID,
        sendermobilenumber: mobile_number
      }
      // to avoid circular dependency
      const Payment = require('./../bankHandler/payment')
      const payment = new Payment(bankHandler.bank_name, bankHandler.transaction_type, this.connection, bankHandler.ma_bank_on_boarding)
      const banklLimitResponse = await payment.requestToBank('GET_REMITTER_BALANCE', apiAPIPayload, 'GETREMITTERBANKLIMIT', this.connection)

      log.logger({ pagename: 'routing.js', action: 'getRemitterBalance', type: 'bankLimt', fields: banklLimitResponse })

      let consumedLimit = 0
      let remainingLimit = max_amount
      if (banklLimitResponse.status == 200) {
        consumedLimit = banklLimitResponse.consumedLimit || 0
        remainingLimit = banklLimitResponse.remainingLimit || max_amount

        // if the remaining Limit is zero from bank side.
        if (banklLimitResponse.remainingLimit == 0) {
          remainingLimit = banklLimitResponse.remainingLimit
        }
      }

      return { status: 200, respcode: 1000, message: errorMsg.responseCode[1000], consumedLimit: consumedLimit, remainingLimit: remainingLimit }
    } catch (error) {
      log.logger({ pagename: 'routing.js', action: 'getRemitterBalance', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    }
  }

  static async getBankHandlerObject ({ ma_bank_on_boarding_id, connection }) {
    const isSet = (connection === null || connection === undefined)
    /* to avoid circular dependency error */
    const mySQLWrapper = require('../../lib/mysqlWrapper')
    const conn = isSet ? await mySQLWrapper.getConnectionFromPool() : connection
    try {
      const fetchBankQuery = `SELECT mbb.ma_bank_on_boarding_id,mbb.bank_name,mbt.transaction_type,mbt.ma_bank_type_id
                                FROM ma_bank_on_boarding mbb INNER JOIN ma_bank_type mbt 
                                ON  mbb.ma_bank_on_boarding_id = mbt.ma_bank_on_boarding_id
                                WHERE mbb.ma_bank_on_boarding_id=${ma_bank_on_boarding_id} AND mbb.onboarding_status = 'Y' LIMIT 1`

      const fetchBankResult = await DAO.rawQuery(fetchBankQuery, conn)

      if (fetchBankResult.length == 0) {
        return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
      }

      const bankHandler = {
        bank_name: fetchBankResult[0].bank_name,
        transaction_type: fetchBankResult[0].transaction_type,
        ma_bank_on_boarding_id: fetchBankResult[0].ma_bank_on_boarding_id,
        ma_bank_type_id: fetchBankResult[0].ma_bank_type_id
      }
      return { status: 200, message: errorMsg.responseCode[100], respcode: 1000, action_code: 1000, bankHandler }
    } catch (error) {
      log.logger({ pagename: 'routing.js', action: 'getBankHandlerObject', type: 'catcherror', fields: error })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001, action_code: 1001 }
    } finally {
      if (isSet) {
        conn.release()
      }
    }
  }
}

module.exports = Routing
