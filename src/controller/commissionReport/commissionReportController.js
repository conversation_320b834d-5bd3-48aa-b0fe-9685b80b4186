const DAO = require('../../lib/dao')
const util = require('../../util/util')
const mySQLWrapper = require('../../lib/mysqlWrapper')
const errorMsg = require('../../util/error')
const log = require('../../util/log')

class commissionReportController extends DAO {
  get TABLE_NAME () {
    return 'ma_orderwise_taxes'
  }

  static get PRIMARY_KEY () {
    return 'ma_user_id'
  }

  static async getCommissionReport (_, fields) {
    log.logger({ pagename: 'commissionReportController.js', action: 'getCommissionReport', type: 'request', fields })

    const connection = await mySQLWrapper.getConnectionFromPool()

    try {
      const today = new Date().toISOString().slice(0, 10)
      const start_date = fields.start_date || today
      const end_date = fields.end_date || today

      const parent_id = fields.parent_id
      const transaction_type = fields.transaction_type
      const ma_user_id = fields.ma_user_id
      const userid = fields.userid
      const limit = fields.limit
      const offset = fields.offset

      // Remove used keys from `fields` to avoid double filtering later
      delete fields.start_date
      delete fields.userid
      delete fields.end_date
      delete fields.parent_id
      delete fields.transaction_type
      delete fields.ma_user_id
      delete fields.limit
      delete fields.offset

      let sql = `
      SELECT 
        SQL_CALC_FOUND_ROWS
        mot.ma_user_id,
        mum.company,
        mum.user_type,
        mum.userid,
        mum.firstname,
        mum.lastname,
        mttm.display_name,
        mot.orderid,
        mot.parent_id,
        mot.gst_amount,
        mtm.amount AS transaction_amount,
        mot.amount AS tax_amount,
        mot.tds_amount,
        mot.ma_status,
        IF(ma_tds_merchant_master.ma_user_id IS NOT NULL AND ma_tds_merchant_master.ma_user_id != '', 'Yes', 'No') AS specific_person,
        (mot.tds_amount / mot.amount) * 100 AS tds_percent,
        DATE_FORMAT(mot.addedon, '%d-%m-%Y %h:%i:%s %p') AS addedon
      FROM ma_orderwise_taxes mot
      LEFT JOIN ma_user_master mum ON mot.ma_user_id = mum.profileid
      LEFT JOIN ma_transaction_master mtm ON mtm.transaction_id = mot.parent_id
      LEFT JOIN ma_transaction_type_master mttm ON mttm.transaction_type = mot.transaction_type
      LEFT JOIN ma_tds_merchant_master 
        ON ma_tds_merchant_master.ma_user_id = mot.ma_user_id 
       AND ma_tds_merchant_master.tds_status = 'active'`

      // Apply fixed filters
      sql += `where mot.addedon BETWEEN '${start_date} 00:00:00' AND '${end_date} 23:59:59'`

      if (parent_id) {
        sql += ` AND mot.parent_id = '${parent_id}'`
      }

      if (transaction_type) {
        sql += ` AND mot.transaction_type = '${transaction_type}'`
      }

      if (ma_user_id) {
        sql += ` AND mot.ma_user_id = '${ma_user_id}'`
      }

      if (userid) {  
        sql += ` AND mum.userid = '${userid}'`
      }

      sql += ` ORDER BY  mot.addedon DESC LIMIT ${offset},${limit}`

      // Apply dynamic filters from remaining fields
      const tableMap = {
        ma_user_id: 'mot',
        orderid: 'mot',
        transaction_type: 'mot',
        userid: 'mum',
        mobile_number: 'mtm',
        company: 'mum',
        firstname: 'mum',
        lastname: 'mum'
      }

      for (const [key, value] of Object.entries(fields)) {
        if (value !== undefined && value !== null && value !== '') {
          const tableAlias = tableMap[key] || 'mot'
          sql += ` AND ${tableAlias}.${key} = '${value}'`
        }
      }
      log.logger({ pagename: __filename, action: 'getCommissionReport', type: 'response', fields: sql })

      const commissionReport = await this.rawQuery(sql, connection)
      log.logger({ pagename: __filename, action: 'getCommissionReport', type: 'response', fields: commissionReport })
      let nextFlag = false
      if (commissionReport.length > 0) {
        const countSql = 'SELECT FOUND_ROWS() AS total'
        log.logger({ pagename: __filename, action: 'getCommissionReport', type: 'countSql', fields: countSql })
        const countResult = global.G_MYSQL_SINGLE ? await this.rawQueryV2(countSql, connection) : await this.rawQuery(countSql, connection)
        log.logger({ pagename: __filename, action: 'getCommissionReport', type: 'countResult', fields: countResult })
        if (countResult.length > 0) {
          const total = countResult[0].total
          if ((limit + offset) < total) {
            nextFlag = true
          }
        }
      }

      const commissionResponse = commissionReport.map(item => ({
        ma_user_id: item.ma_user_id,
        company: item.company,
        user_type: item.user_type,
        merchant_name: `${item.firstname} ${item.lastname}`,
        transaction_type: item.display_name,
        parent_id: item.parent_id,
        order_id: item.orderid,
        transaction_amount: Number(item.transaction_amount.toFixed(2)),
        amount: Number(item.tax_amount.toFixed(2)),
        gst_amount: Number(item.gst_amount.toFixed(2)),
        tds_amount: Number(item.tds_amount.toFixed(2)),
        tds_percent: item.tds_percent,
        specific_person: item.specific_person,
        record_status: item.ma_status,
        start_date: start_date,
        end_date: end_date,
        addedon: item.addedon
      }))

      return {
        status: 200,
        message: errorMsg.responseCode[1000],
        respcode: 1000,
        nextFlag,
        commission_report: commissionResponse
      }
    } catch (err) {
      log.logger({ pagename: __filename, action: 'getCommissionReport', type: 'err', fields: err })
      return { status: 400, message: errorMsg.responseCode[1001], respcode: 1001 }
    } finally {
      if (connection) connection.release()
    }
  }
}
module.exports = commissionReportController
