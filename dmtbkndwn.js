'use strict'

const { serverlessErrorHandler } = require('./src/util/errorHandler')
var bankListController = require('./src/controller/banklist/banklistController.js')
const log = require('./src/util/log')

module.exports.dmtbkndwn = async (event, context, callback) => {
  console.log('---RUNNING CRON TO CHECK BANK SERVER STATUS---')
  // const originalLogger = log.logger
  // log.logger = (logData) => {
  //   originalLogger({ ...logData, awsRequestId: context.awsRequestId })
  // }
  log.setAwsRequestId(context.awsRequestId)

  var payload = {}
  try {
    var response = ''
    response = await bankListController.checkdmtbankdown()
    console.log(response)
    // response = await serverlessErrorHandler(event, response)

    console.log('---BANK DOWN CRON FINAL RESPONSE---', response)
    if (process.env.IS_OFFLINE === 'true') {
      callback(null, { statusCode: 200, body: JSON.stringify(response) })
    }
    return response
  } catch (error) {
    console.log('---BANK DOWN CRON CATCH ERROR---', error)
    var errResp = {}
    errResp = { status: 400, message: 'Request Failed' }
    console.log('res1', errResp)
    return errResp
  }
}
